<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aJ="INSERT";var aa=lang["DlgComInsert"];var t;var bJ="http://";var bO="200";var bD="200";var cD="";var cj="";var du="";var gO="true";var fZ="true";var bj="";var lY="";var mN="high";var mX="";var kD="";var nd="";var et="file";var cu=((parseFloat(config.AllowFlashSize)>0)?true:false);vh();var bm=lang["DlgFlash"]+"("+aa+")";document.write("<title>"+bm+"</title>");function vh(){if(C.ai()!="Control"){return;}t=C.ax();if(t.tagName=="IMG"&&xV(t)=="flash"){aJ="MODI";}if(aJ!="MODI"){return;}aa=lang["DlgComModify"];et="url";cD=dE(t,"align");bO=lZ(eZ(t,"width"));bD=lZ(eZ(t,"height"));cj=dE(t,"vspace");du=dE(t,"hspace");aj.Init(wi(t));if(aj.oA=="object"){bJ=aj.GetValue("movie");}else{bJ=aj.GetValue("src");}gO=aj.GetValue("loop");if(gO=="-1"||gO=="1"){gO="true";}else if(gO=="0"){gO="false";}fZ=aj.GetValue("play");if(fZ=="-1"||fZ=="1"){fZ="true";}else if(fZ=="0"){fZ="false";}bj=aj.GetValue("bgcolor");lY=aj.GetValue("scale");mN=aj.GetValue("quality");mX=aj.GetValue("menu");kD=aj.GetValue("wmode");nd=aj.GetValue("flashvars");};function aq(){lang.ag(document);aC($("d_align"),cD.toLowerCase());aC($("d_loop"),gO.toLowerCase());aC($("d_play"),fZ.toLowerCase());aC($("d_scale"),lY.toLowerCase());aC($("d_quality"),mN.toLowerCase());aC($("d_menu"),mX.toLowerCase());aC($("d_wmode"),kD.toLowerCase());if(!cu){et="url";}bP(et);$("d_fromurl").value=bJ;$("d_bgcolor").value=bj;$("s_bgcolor").style.backgroundColor=bj;$("d_width").value=bO;$("d_height").value=bD;$("d_vspace").value=cj;$("d_hspace").value=du;$("d_flashvars").value=nd;var o1=$("TD_Right");var o2=$("Fieldset_Right");var h1=o1.offsetHeight;var h2=o2.offsetHeight;if(h1>h2){if(F.as){o2.style.height=h1+"px";}else{o2.style.height=(h1-2)+"px";}}eT();if($("d_fromurl")){if(F.as){$("d_fromurl").onpropertychange=nY;}else{R.az($("d_fromurl"),"input",nY);}}parent.ar(bm);if(cu&&document.documentMode==5){var vt=(et=="url")?false:true;$("uploadfile").disabled=vt;$("uploadfile").disabled= !vt;}};function nY(){if(F.as){if(event.propertyName!='value'){return;}}eT();};function bP(what){if(what=="url"){$("d_fromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}$("d_checkfromurl").checked=true;if(cu){$("d_checkfromfile").checked=false;$("uploadfile").disabled=true;}}else{$("d_checkfromurl").checked=false;$("uploadfile").disabled=false;$("d_checkfromfile").checked=true;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}}};function UploadError(an){gd();bP('file');$("divProcessing").style.display="none";try{bX($("uploadfile"),md(an,config.AllowFlashExt,config.AllowFlashSize));}catch(e){}};function UploadSaved(gS){$("d_fromurl").value=gS;eH();};function eH(){bJ=fk($("d_fromurl").value);bj=$("d_bgcolor").value;cD=$("d_align").value;bO=$("d_width").value;bD=$("d_height").value;cj=$("d_vspace").value;du=$("d_hspace").value;gO=$("d_loop").value;fZ=$("d_play").value;lY=$("d_scale").value;mN=$("d_quality").value;mX=$("d_menu").value;kD=$("d_wmode").value;nd=$("d_flashvars").value;if(aJ=="MODI"){t.style.width=bO;t.style.height=bD;bq(t,'align',cD);bq(t,'vspace',cj);bq(t,'hspace',du);aj.jk('bgcolor',bj);aj.jk('loop',gO);aj.jk('play',fZ);aj.jk('scale',lY);aj.jk('quality',mN);aj.jk('menu',mX);aj.jk('wmode',kD);aj.jk('flashvars',nd);zG(t,aj.GetHtml())}else{var V="<embed type=\"application/x-shockwave-flash\" pluginspage=\"http://www.macromedia.com/go/getflashplayer\"";if(cD!=""){V+=" align=\""+cD+"\"";}if(bO!=""){V+=" width=\""+bO+"\"";}if(bD!=""){V+=" height=\""+bD+"\"";}if(cj!=""){V+=" vspace=\""+cj+"\"";}if(du!=""){V+=" hspace=\""+du+"\"";}V+=" src=\""+bJ+"\"";if(bj!=""){V+=" bgcolor=\""+bj+"\"";}if(gO!=""){V+=" loop=\""+gO+"\"";}if(fZ!=""){V+=" play=\""+fZ+"\"";}if(lY!=""){V+=" scale=\""+lY+"\"";}if(mN!=""){V+=" quality=\""+mN+"\"";}if(mX!=""){V+=" menu=\""+mX+"\"";}if(kD!=""){V+=" wmode=\""+kD+"\"";}if(nd!=""){V+=" flashvars=\""+nd+"\"";}V+="></embed>";EWIN.insertHTML(V);}parent.bV();};function ok(){$("d_width").value=fe($("d_width").value);$("d_height").value=fe($("d_height").value);$("d_vspace").value=fe($("d_vspace").value);$("d_hspace").value=fe($("d_hspace").value);if($("d_checkfromurl").checked){eH();}else{if(!iV($("uploadfile").value,config.AllowFlashExt)){UploadError("ext");return false;}he();$("divProcessing").style.display="";document.myuploadform.submit();}};function he(){$("d_checkfromfile").disabled=true;$("d_checkfromurl").disabled=true;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}$("d_bgcolor").disabled=true;$("d_align").disabled=true;$("d_width").disabled=true;$("d_height").disabled=true;$("d_vspace").disabled=true;$("d_hspace").disabled=true;$("d_loop").disabled=true;$("d_play").disabled=true;$("d_scale").disabled=true;$("d_quality").disabled=true;$("d_menu").disabled=true;$("d_wmode").disabled=true;$("d_flashvars").disabled=true;$("d_ok").disabled=true;};function gd(){$("d_checkfromfile").disabled=false;$("d_checkfromurl").disabled=false;$("d_fromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}$("d_bgcolor").disabled=false;$("d_align").disabled=false;$("d_width").disabled=false;$("d_height").disabled=false;$("d_vspace").disabled=false;$("d_hspace").disabled=false;$("d_loop").disabled=false;$("d_play").disabled=false;$("d_scale").disabled=false;$("d_quality").disabled=false;$("d_menu").disabled=false;$("d_wmode").disabled=false;$("d_flashvars").disabled=false;$("d_ok").disabled=false;};function eT(){var aG,v;if($("d_checkfromurl").checked){v=$("d_fromurl").value;aG=v;if(config.BaseHref!=""){aG=qf(aG);}}else{v=$("uploadfile").value;aG="file:///"+v;}var cs="";if(v.length>4){cs=v.substr(v.length-4).toLowerCase();}if(cs!=".swf"){$("tdPreview").innerHTML="";return;}if(!$("d_checkfromurl").checked&&F.as){if(cs==".swf"){if(!FT.FV(true)){return;}var h=FT.kI.GetFlashHeader(v);if(FT.Fi()){return;}if(h){var a=h.split("|");$("d_width").value=a[0];$("d_height").value=a[1];}}}$("tdPreview").innerHTML="";var V="";V="<embed src=\""+aG+"\" quality=\"high\" wmode=\"transparent\" width=\"180\" height=\"200\" type=\"application/x-shockwave-flash\" pluginspage=\"http://www.macromedia.com/go/getflashplayer\"></embed>";$("tdPreview").innerHTML=V;};function jS(dO,iq,bZ){if(bZ=="tab_mfu"){iU.Load("flash",$(bZ),ck.jp[1].Width+"px",ck.jp[1].Height+"px");}};function MFUReturn(bv){var V="";var eG=bv.split("|");for(var i=0;i<eG.length;i++){var a=eG[i].split("::");if(a.length==3&&a[1]!=""){var cf=a[0].substr(a[0].lastIndexOf("\\")+1);var gI=a[1];var cs=gI.substr(gI.lastIndexOf(".")+1);var eh="200";var ff="200";if(cs.toLowerCase()=="swf"){var aI=FT.kI.GetFlashHeader(cf);if(!FT.Fi()){if(aI){var gG=aI.split("|");eh=gG[0];ff=gG[1];}}}V+='<embed src="'+gI+'" quality="high" loop="true" play="true" width="'+eh+'" height="'+ff+'" type="application/x-shockwave-flash" pluginspage="http://www.macromedia.com/go/getflashplayer"></embed><br>';EWIN.addUploadFile(cf,gI);}}EWIN.insertHTML(V);parent.bV();} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <script type="text/javascript"> if(config.MFUEnable=="1"){ck.lQ([[lang["DlgComTabNormal"],"tab_normal"],[lang["DlgComTabMFU"],"tab_mfu"]]);} </script> <table id="tab_normal" border=0 cellpadding=0 cellspacing=5 align=center> <tr valign=top><td> <table border=0 cellpadding=0 cellspacing=0 align=center width="100%"> <tr> <td> <fieldset> <legend><span lang=DlgFlashSource></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=0 width="300px"><tr><td></td></tr></table> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <script type="text/javascript"> if(cu){document.write("<tr>");document.write("<td noWrap width=\"20%\"><input type=radio id=\"d_checkfromfile\" value=\"1\" onclick=\"bP('file')\"><label for=d_checkfromfile>"+lang["DlgFromFile"]+"</label>:</td>");document.write("<td noWrap width=\"80%\">");document.write(lN("flash"));document.write("</td>");document.write("</tr>");} </script> <tr> <td noWrap width="20%"><input type=radio id="d_checkfromurl" value="1" onclick="bP('url')"><label for=d_checkfromurl><span lang=DlgFromUrl></span></label>:</td> <td noWrap width="80%"> <script type="text/javascript"> if(config.AllowBrowse=="1"){document.write("<table border=0 cellpadding=0 cellspacing=0 width='100%'><tr><td width='100%'><input type=text id='d_fromurl' style='width:100%' size=20 value=''></td><td><input class='dlgBtnBrowse' type=button id='d_browse' onclick=\"nf('flash','fromurl')\" value='"+lang["DlgBtnBrowse"]+"' align=absmiddle></td></tr></table>");}else{document.write("<input type=text id='d_fromurl' style='width:100%' size=30 value=''>");} </script> </td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <fieldset> <legend><span lang=DlgFlashEffect></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgFlashPlay></span>:</td> <td noWrap width="29%"> <select id=d_play size=1 style="width:80px"> <option value='' selected lang=DlgComDefault></option> <option value='true' lang=DlgComYes></option> <option value='false' lang=DlgComNo></option> </select> </td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgFlashLoop></span>:</td> <td noWrap width="29%"> <select id=d_loop size=1 style="width:80px"> <option value='' selected lang=DlgComDefault></option> <option value='true' lang=DlgComYes></option> <option value='false' lang=DlgComNo></option> </select> </td> </tr> <tr> <td noWrap width="20%"><span lang=DlgFlashMenu></span>:</td> <td noWrap width="29%"> <select id=d_menu size=1 style="width:80px"> <option value='' selected lang=DlgComDefault></option> <option value='true' lang=DlgComYes></option> <option value='false' lang=DlgComNo></option> </select> </td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgFlashWMode></span>:</td> <td noWrap width="29%"> <select id=d_wmode size=1 style="width:80px"> <option value='' selected lang=DlgComDefault></option> <option value='window' lang=DlgFlashWmWindow></option> <option value='opaque' lang=DlgFlashWmOpaque></option> <option value='transparent' lang=DlgFlashWmTransparent></option> </select> </td> </tr> <tr> <td noWrap width="20%"><span lang=DlgFlashQuality></span>:</td> <td noWrap width="29%"> <select id=d_quality size=1 style="width:80px"> <option value='' selected lang=DlgComDefault></option> <option value='low' lang=DlgFlashQuLow></option> <option value='autolow' lang=DlgFlashQuAutoLow></option> <option value='medium' lang=DlgFlashQuMedium></option> <option value='autohigh' lang=DlgFlashQuAutoHigh></option> <option value='high' lang=DlgFlashQuHigh></option> <option value='best' lang=DlgFlashQuBest></option> </select> </td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgFlashScale></span>:</td> <td noWrap width="29%"> <select id=d_scale size=1 style="width:80px"> <option value='' selected lang=DlgComDefault></option> <option value='showall' lang=DlgFlashScShowall></option> <option value='noborder' lang=DlgFlashScNoborder></option> <option value='exactfit' lang=DlgFlashScExactfit></option> </select> </td> </tr> <tr> <td noWrap width="20%"><span lang=DlgAlign></span>:</td> <td noWrap width="29%"> <select id=d_align size=1 style="width:80px"> <option value='' selected lang=DlgComDefault></option> <option value='left' lang=DlgAlignLeft></option> <option value='right' lang=DlgAlignRight></option> <option value='top' lang=DlgAlignTop></option> <option value='middle' lang=DlgAlignMiddle></option> <option value='bottom' lang=DlgAlignBottom></option> <option value='absmiddle' lang=DlgAlignAbsmiddle></option> <option value='absbottom' lang=DlgAlignAbsbottom></option> <option value='baseline' lang=DlgAlignBaseline></option> <option value='texttop' lang=DlgAlignTexttop></option> </select> </td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgComBgColor></span>:</td> <td noWrap width="29%"><input style="width:62px" type=text id=d_bgcolor size=7 value=""><img border=0 src="images/rect.gif" width=18 style="cursor:hand" id=s_bgcolor onclick="hu('bgcolor')" align=absmiddle></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgComWidth></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_width size=10 value=""></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgComHeight></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_height size=10 value=""></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgComVSpace></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_vspace size=10 value="" onkeydown="eR(event);"></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgComHSpace></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_hspace size=10 value="" onkeydown="eR(event);"></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgFlashVars title="FlashVars"></span>:</td> <td noWrap width="80%" colspan=4><input type=text id=d_flashvars size=10 style="width:100%"></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> </table> </td><td id="TD_Right"> <fieldset id="Fieldset_Right"> <legend><span lang=DlgComPreview></span></legend> <table border=0 cellpadding=0 cellspacing=5 width="200" height="240" valign=top id=tablePreview> <tr><td colspan=2 bgcolor=#FFFFFF align=center valign=middle id=tdPreview height="100%"> </td></tr> <tr><td id=tdPreviewSize></td><td align=right><input type=button class="dlgBtn" id=btnPreivew onclick="eT()" lang=DlgComPreview></td></tr> </table> </fieldset> </td></tr> <tr><td noWrap align=right colspan=2><input type=submit class="dlgBtnCommon dlgBtn" value='' id=d_ok onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="$('tdPreview').innerHTML='';parent.bn();" lang=DlgBtnCancel></td></tr> </table> <div id="tab_mfu" style="display:none"></div> </td></tr></table> <div id=divProcessing style="width:200px;height:30px;position:absolute;left:70px;top:100px;display:none"> <table border=0 cellpadding=0 cellspacing=1 bgcolor="#000000" width="100%" height="100%"><tr><td bgcolor=#3A6EA5><marquee align="middle" behavior="alternate" scrollamount="5"><font color=#FFFFFF><span lang=DlgComUploading></span></font></marquee></td></tr></table> </div> </body> </html>