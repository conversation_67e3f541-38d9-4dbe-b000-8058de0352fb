<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aJ=cc['action'];var aa=lang["DlgComInsert"];var t;var jx="2";var pq="2";var cD="";var fD="1";var lz="";var nc="collapse";var nt="3";var mw="2";var bO="";var bD="";var dr="#000000";var bj="#FFFFFF";var bx="";var dH="";var cS="";var hC="";var gW="%";var kW=true;var gP=false;var fb="100";var gH="%";var ke=false;var jw=true;var eb="";if(aJ=="modify"){if(C.ai()=="Control"){t=C.ax();if(t.tagName!="TABLE"){t=null;}}else{t=na(C.cI(),"TABLE");}if(t){aJ="MODI";aa=lang["DlgComModify"];jx=t.rows.length;pq=vD(t);cD=dE(t,"align");fD=dE(t,"border");lz=t.style.textAlign;nc=t.style.borderCollapse;nt=dE(t,"cellPadding");mw=dE(t,"cellSpacing");bO=eZ(t,"width");bD=eZ(t,"height");dr=dE(t,"borderColor");bj=eZ(t,"background-color");bx=t.style.backgroundImage;dH=t.style.backgroundRepeat;cS=t.style.backgroundAttachment;hC=t.style.borderStyle;bx=bx.replace(/\"/gi,"");bx=bx.substr(4,bx.length-5);}}function na(obj,iI){while(obj!=null&&obj.tagName!=iI){obj=obj.parentNode;if(!obj|| !obj.tagName){obj=null;break;}}return obj;};var bm=lang["DlgTab"]+"("+aa+")";document.write("<title>"+bm+"</title>");function aq(){lang.ag(document);aC($("d_align"),cD.toLowerCase());aC($("d_aligntext"),lz.toLowerCase());aC($("d_bordercollapse"),nc.toLowerCase());aC($("d_borderstyle"),hC.toLowerCase());if(aJ=="MODI"){if(bO==""){kW=false;gP=true;fb="100";gW="%";}else{kW=true;gP=false;if(bO.substr(bO.length-1)=="%"){fb=bO.substring(0,bO.length-1);gW="%";}else{gW="";fb=parseInt(bO);if(isNaN(fb)){fb="";}}}if(bD==""){ke=false;jw=true;eb="100";gH="%";}else{ke=true;jw=false;if(bD.substr(bD.length-1)=="%"){eb=bD.substring(0,bD.length-1);gH="%";}else{gH="";eb=parseInt(bD);if(isNaN(eb)){eb="";}}}}switch(gW){case "%":$("d_widthunit").selectedIndex=1;break;default:gW="";$("d_widthunit").selectedIndex=0;break;}switch(gH){case "%":$("d_heightunit").selectedIndex=1;break;default:gH="";$("d_heightunit").selectedIndex=0;break;}$("d_row").value=jx;$("d_col").value=pq;$("d_border").value=fD;$("d_cellspacing").value=mw;$("d_cellpadding").value=nt;$("d_widthvalue").value=fb;$("d_widthvalue").disabled=gP;$("d_widthunit").disabled=gP;$("d_heightvalue").value=eb;$("d_heightvalue").disabled=jw;$("d_heightunit").disabled=jw;$("d_bordercolor").value=dr;$("s_bordercolor").style.backgroundColor=dr;$("d_bgcolor").value=bj;$("s_bgcolor").style.backgroundColor=bj;$("d_widthcheck").checked=kW;$("d_heightcheck").checked=ke;$("d_image").value=bx;$("d_repeat").value=dH;$("d_attachment").value=cS;parent.ar(bm);};function fd(obj,kR){var b=false;if(obj.value!=""){obj.value=parseFloat(obj.value);if(obj.value!="0"){b=true;}}if(b==false){bX(obj,kR);return false;}return true;};function vD(cF){var wX=0;if(cF!=null){for(var i=0;i<cF.rows.length;i++){if(cF.rows[i].cells.length>wX){wX=cF.rows[i].cells.length;}}}return wX;};function Cd(cF){if(cF){var xN=cF.insertRow(-1);for(var i=0;i<cF.rows[0].cells.length;i++){var sp=xN.insertCell(-1);sp.innerHTML="&nbsp;";}}};function AV(cF){if(cF){for(var i=0;i<cF.rows.length;i++){var sp=cF.rows[i].insertCell(-1);sp.innerHTML="&nbsp;"}}};function zX(cF){if(cF){cF.deleteRow(-1);}};function zV(cF){if(cF){for(var i=0;i<cF.rows.length;i++){cF.rows[i].deleteCell(-1);}}};function ok(){dr=$("d_bordercolor").value;bj=$("d_bgcolor").value;if(!fd($("d_row"),lang["DlgTabInvalidRow"])){return;}if(!fd($("d_col"),lang["DlgTabInvalidCol"])){return;}var bO="";if($("d_widthcheck").checked){if(!fd($("d_widthvalue"),lang["DlgTabInvalidWidth"])){return;}bO=$("d_widthvalue").value+$("d_widthunit").value;}var bD="";if($("d_heightcheck").checked){if(!fd($("d_heightvalue"),lang["DlgTabInvalidHeight"])){return;}bD=$("d_heightvalue").value+$("d_heightunit").value;}jx=$("d_row").value;pq=$("d_col").value;cD=$("d_align").options[$("d_align").selectedIndex].value;fD=$("d_border").value;lz=$("d_aligntext").options[$("d_aligntext").selectedIndex].value;nc=$("d_bordercollapse").options[$("d_bordercollapse").selectedIndex].value;nt=$("d_cellpadding").value;mw=$("d_cellspacing").value;bx=$("d_image").value;dH=$("d_repeat").value;cS=$("d_attachment").value;hC=$("d_borderstyle").options[$("d_borderstyle").selectedIndex].value;if(bx!=""){bx="url("+bx+")";}if(aJ=="MODI"){var iS=jx-t.rows.length;if(iS>0){for(var i=0;i<iS;i++){Cd(t);}}else{for(var i=0;i>iS;i--){zX(t);}}var iS=pq-vD(t);if(iS>0){for(var i=0;i<iS;i++){AV(t);}}else{for(var i=0;i>iS;i--){zV(t);}}t.style.width=bO;bq(t,"width","");t.style.height=bD;bq(t,"height","");bq(t,"align",cD);bq(t,"border",fD);t.style.textAlign=lz;t.style.borderCollapse=nc;bq(t,"cellSpacing",mw);bq(t,"cellPadding",nt);bq(t,"borderColor",dr);t.style.backgroundColor=bj;bq(t,"bgColor","");t.style.backgroundImage=bx;t.style.backgroundRepeat=dH;t.style.backgroundAttachment=cS;t.style.borderStyle=hC;}else{var gQ='<table';if(cD!=''){gQ+=' align="'+cD+'"';}if(fD!=''){gQ+=' border="'+fD+'"';}if(nt!=''){gQ+=' cellpadding="'+nt+'"';}if(mw!=''){gQ+=' cellspacing="'+mw+'"';}if(dr!=''){gQ+=' bordercolor="'+dr+'"';}var ak='';if(bO!=''){ak+='width:'+bO+';';}if(bD!=''){ak+='height:'+bD+';';}if(bj!=''){ak+='background-color:'+bj+';';}if(bx!=''){ak+='background-image:'+bx+';';}if(dH!=''){ak+='background-repeat:'+dH+';';}if(cS!=''){ak+='background-attachment:'+cS+';';}if(hC!=''){ak+='border-style:'+hC+';';}if(lz!=''){ak+='text-align:'+lz+';';}if(nc!=''){ak+='border-collapse:'+nc+';';}if(ak!=''){gQ+=' style="'+ak+'"';}gQ+='>';for(var i=1;i<=jx;i++){gQ+='<tr>';for(var j=1;j<=pq;j++){gQ+='<td>&nbsp;</td>';}gQ+='</tr>';}gQ+='</table>';EWIN.insertHTML(gQ);}parent.bV();} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td> <fieldset> <legend><span lang=DlgTabRowsCols></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgTabRows></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_row size=10 value="" onkeydown="eR(event)"></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgTabCols></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_col size=10 value="" onkeydown="eR(event)"></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <fieldset> <legend><span lang=DlgTabLayout></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgAlign></span>:</td> <td noWrap width="29%"> <select id="d_align" style="width:80px"> <option value='' lang=DlgComDefault></option> <option value='left' lang=DlgAlignLeft></option> <option value='center' lang=DlgAlignCenter></option> <option value='right' lang=DlgAlignRight></option> </select> </td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgTabBorder></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_border size=10 value="" onkeydown="eR(event)"></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgAlignText></span>:</td> <td noWrap width="29%"> <select id="d_aligntext" style="width:80px"> <option value='' lang=DlgComDefault></option> <option value='left' lang=DlgAlignLeft></option> <option value='center' lang=DlgAlignCenter></option> <option value='right' lang=DlgAlignRight></option> <option value='justify' lang=DlgAlignFull></option> </select> </td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgTabBorderCollapse></span>:</td> <td noWrap width="29%"> <select id="d_bordercollapse" style="width:80px"> <option value='' lang=DlgComDefault></option> <option value='separate' lang=DlgTabBCseparate></option> <option value='collapse' lang=DlgTabBCcollapse></option> </select> </td> </tr> <tr> <td noWrap width="20%"><span lang=DlgTabCellspacing></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_cellspacing size=10 value="" onkeydown="eR()"></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgTabCellpadding></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_cellpadding size=10 value="" onkeydown="eR()"></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <fieldset> <legend><span lang=DlgTabSize></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap><input id="d_widthcheck" type="checkbox" onclick="$('d_widthvalue').disabled=(!this.checked);$('d_widthunit').disabled=(!this.checked);" value="1"> <label for=d_widthcheck><span lang=DlgTabChkWidth></span></label></td> <td noWrap align=right> <input id="d_widthvalue" type="text" value="" size="5"> <select id="d_widthunit" size=1 style="width:65px"> <option value='px' lang=DlgComPx></option><option value='%' lang=DlgComPer></option> </select> </td> </tr> <tr> <td noWrap><input id="d_heightcheck" type="checkbox" onclick="$('d_heightvalue').disabled=(!this.checked);$('d_heightunit').disabled=(!this.checked);" value="1"> <label for=d_heightcheck><span lang=DlgTabChkHeight></span></label></td> <td noWrap align=right> <input id="d_heightvalue" type="text" value="" size="5"> <select id="d_heightunit" size=1 style="width:65px"> <option value='px' lang=DlgComPx></option><option value='%' lang=DlgComPer></option> </select> </td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <fieldset> <legend><span lang=DlgTabStyle></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgColorBorder></span>:</td> <td noWrap width="29%"><input style="width:62px" type=text id=d_bordercolor size=7 value=""><img border=0 src="images/rect.gif" width=18 style="cursor:hand" id=s_bordercolor onclick="hu('bordercolor')" align=absmiddle></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgTabBorderStyle></span>:</td> <td noWrap width="29%"> <select id=d_borderstyle size=1 style="width:80px"> <option value="" lang=DlgComDefault></option> <option value="solid" lang=DlgLineSolid></option> <option value="dotted" lang=DlgLineDotted></option> <option value="dashed" lang=DlgLineDashed></option> <option value="double" lang=DlgLineDouble></option> <option value="groove" lang=DlgLineGroove></option> <option value="ridge" lang=DlgLineRidge></option> <option value="inset" lang=DlgLineInset></option> <option value="outset" lang=DlgLineOutset></option> </select> </td> </tr> <tr> <td noWrap width="20%"><span lang=DlgColorBg></span>:</td> <td noWrap width="29%"><input style="width:62px" type=text id=d_bgcolor size=7 value=""><img border=0 src="images/rect.gif" width=18 style="cursor:hand" id=s_bgcolor onclick="hu('bgcolor')" align=absmiddle></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgTabBgImage></span>:</td> <td noWrap width="29%"><input style="width:62px" type=text id=d_image size=7 value=""><input type=hidden id=d_repeat><input type=hidden id=d_attachment><img border=0 src="images/rectimg.gif" width=18 style="cursor:hand" id=s_bgimage onclick="vo()" align=absmiddle></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr><td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel></td></tr> </table> </td></tr></table> </body> </html>