<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var bm=lang["DlgCelSpt"];document.write("<title>"+bm+"</title>");function fd(obj,kR){var b=false;if(obj.value!=""){obj.value=parseFloat(obj.value);if(obj.value!="0"){b=true;}}if(b==false){bX(obj,kR);return false;}return true;};function uM(mb){if(mb=="col"){$("d_col").checked=true;$("d_row").checked=false;}else{$("d_col").checked=false;$("d_row").checked=true;}if($("d_col").checked){$("d_view").innerHTML="<table border=1 cellpadding=0><tr><td width=25>&nbsp;</td><td width=25>&nbsp;</td></tr></table>";$("d_label").innerHTML="&nbsp;&nbsp;&nbsp;&nbsp;"+lang["DlgCelSptCols"]+":";}if($("d_row").checked){$("d_view").innerHTML="<table border=1 cellpadding=0 width=50><tr><td>&nbsp;</td></tr><tr><td>&nbsp;</td></tr></table>";$("d_label").innerHTML="&nbsp;&nbsp;&nbsp;&nbsp;"+lang["DlgCelSptRows"]+":";}};function ok(){if(!fd($("d_num"),lang["DlgCelSptErrRowCol"]))return;if($("d_row").checked){EWIN.bI.pI(parseInt($("d_num").value));}if($("d_col").checked){EWIN.bI.qY(parseInt($("d_num").value));}parent.bV();};function aq(){lang.ag(document);uM('col');parent.ar(bm);} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center width=180 height=150> <tr> <td> <table border=0 cellpadding=0 cellspacing=0> <tr><td colspan=3 height=5></td></tr> <tr><td noWrap><input type=radio id=d_col checked onclick="uM('col')"><label for="d_col"><span lang=DlgCelSptSplitCol></span></label></td><td rowspan=3 width=30>&nbsp;</td><td width=60 rowspan=3 id=d_view valign=middle align=center></td></tr> <tr><td height=5></td></tr> <tr><td noWrap><input type=radio id=d_row onclick="uM('row')"><label for="d_row"><span lang=DlgCelSptSplitRow></span></label></td></tr> <tr><td height=5 colspan=3></td></tr> <tr> <td noWrap id=d_label></td> <td></td> <td><input type=text id=d_num size=8 value="2"></td> </tr> </table> </tr> <tr><td height=5></td></tr> <tr><td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel></td></tr> </table> </td></tr></table> </body> </html>