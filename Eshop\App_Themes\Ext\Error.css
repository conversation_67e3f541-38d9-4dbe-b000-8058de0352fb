﻿*{margin:0px;padding:0px;}
body {background:#F2F7FB url(../../images/Error/regbg.jpg) repeat-x top;font-size:12px;text-align:center;color:#333333;}
.L { float:left;}
.R { float:right;}
a {color:#2b2b2b;text-decoration:none;}
a:visited {color:#2b2b2b;text-decoration:none;}
a:hover {color:#ba2636;text-decoration:underline;}
a:active {color:#ba2636;} 
.topLogo { height:50px;}
.LogoL a,.LogoLs a { display:block; float:left;height:50px;}
.LogoLs { width:230px; height:50px;}
.adRRs { width:500px;height:50px; text-align:right;}
.adRRs .reg{ height:30px; line-height:30px; margin-top:20px;}
.reg a { margin-left:10px;}
.regH2,.regH3 { height:40px; clear:both;}
.regH2 { padding-left:150px;}
.regH a,.regH2 a,.regH3 a {color: #1F80CF;text-decoration: none;}
.regH3 { text-align:right; padding-right:100px;}
.HregM  {height:33px;line-height:33px;display:block;background: url(../../images/Error/regth.jpg) repeat-x 0px -102px;}
.HregM .L {height:33px;background: url(../../images/Error/regth.jpg) no-repeat left -170px; padding-left:10px;font-weight: bold;}
.HregM .reR { width:510px;display:block;height:33px;background: url(../../images/Error/regth.jpg) no-repeat right  -204px;}
.regtop { padding:20px; line-height:40px;border:1px solid #D1D1D1;border-top:0px solid #D1D1D1;clear:both;background-color: #FFFFFF;}
.regil {line-height: 60px;height: 60px; padding-left:80px; font-size:18px;font-weight: bold;}
.Error {background: url(../../images/Error/register-bg.jpg) no-repeat 10px -70px; margin-left:130px;}
.regH {padding-left:150px; line-height:25px; padding:30px 0px  10px 150px;}
.bottbg {line-height:25px;padding:10px 0px;text-align: center; padding-right:15px;}
.web {width:800px;margin:0px auto; clear:both; text-align:left;}