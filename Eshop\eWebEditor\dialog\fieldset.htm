<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aJ="INSERT";var aa=lang["DlgComInsert"];var t;var mH;var wI;var wO;var oO="";var oq="";var dr="";var bj="";if(C.ai()=="Control"){t=C.ax();if(t.tagName=="FIELDSET"){aJ="MODI";aa=lang["DlgComModify"];oO=t.align;mH=vK(t,"LEGEND");if(mH){oq=mH.align;}dr=t.style.borderColor;bj=t.style.backgroundColor;}}var bm=lang["DlgFs"]+"("+aa+")";document.write("<title>"+bm+"</title>");function aq(){lang.ag(document);aC($("d_alignfieldset"),oO.toLowerCase());aC($("d_alignlegend"),oq.toLowerCase());$("d_bordercolor").value=dr;$("s_bordercolor").style.backgroundColor=dr;$("d_bgcolor").value=bj;$("s_bgcolor").style.backgroundColor=bj;parent.ar(bm);};function vK(obj,vB){var el;for(var i=0;i<obj.children.length;i++){if(obj.children[i].tagName==vB){return obj.children[i];}else{el=vK(obj.children[i],vB);if(el){return el;}}}return null;};function ok(){dr=$("d_bordercolor").value;if(!oV(dr)){bX($("d_bordercolor"),lang["ErrColorBorder"]);return;}bj=$("d_bgcolor").value;if(!oV(bj)){bX($("d_bgcolor"),lang["ErrColorBg"]);return;}oO=$("d_alignfieldset").options[$("d_alignfieldset").selectedIndex].value;oq=$("d_alignlegend").options[$("d_alignlegend").selectedIndex].value;if(aJ=="MODI"){t.align=oO;if(mH){mH.align=oq;}t.style.borderColor=dr;t.style.backgroundColor=bj;}else{EWIN.insertHTML("<fieldset align='"+oO+"' style='border-color:"+dr+";background-color:"+bj+"'><legend align="+oq+">"+lang["DlgComTitle"]+"</legend>"+lang["DlgComContent"]+"</fieldset>");}parent.bV();} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td> <fieldset> <legend><span lang=DlgAlign></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgFsFieldset></span>:</td> <td noWrap width="29%"><select id=d_alignfieldset size=1 style="width:80px"><option value='' lang=DlgComDefault></option><option value='left' lang=DlgAlignLeft></option><option value='center' lang=DlgAlignCenter></option><option value='right' lang=DlgAlignRight></option></select></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgFsLegend></span>:</td> <td noWrap width="29%"><select id=d_alignlegend size=1 style="width:80px"><option value='' lang=DlgComDefault></option><option value='left' lang=DlgAlignLeft></option><option value='center' lang=DlgAlignCenter></option><option value='right' lang=DlgAlignRight></option></select></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <fieldset> <legend><span lang=DlgColor></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgColorBorder></span>:</td> <td noWrap width="29%"><input style="width:62px" type=text id=d_bordercolor size=7 value=""><img border=0 src="images/rect.gif" width=18 style="cursor:hand" id=s_bordercolor onclick="hu('bordercolor')" align=absmiddle></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgColorBg></span>:</td> <td noWrap width="29%"><input style="width:62px" type=text id=d_bgcolor size=7 value=""><img border=0 src="images/rect.gif" width=18 style="cursor:hand" id=s_bgcolor onclick="hu('bgcolor')" align=absmiddle></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr><td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id=d_ok lang=DlgBtnOK onclick="ok()">&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel></td></tr> </table> </td></tr></table> </body> </html>