<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var et="file";var cu=((parseFloat(config.AllowImageSize)>0)?true:false);var bx=dP.$("d_image").value;var bm=lang["Dlgc_codeImg"];document.write("<title>"+bm+"</title>");function aq(){lang.ag(document);$("d_fromurl").value=bx;if(bx){et="url";}if((!cu)&&(et=="file")){et="url";}bP(et);parent.ar(bm);if(cu&&document.documentMode==5){var vt=(et=="url")?false:true;$("uploadfile").disabled=vt;$("uploadfile").disabled= !vt;}};function bP(an){switch(an){case "url":$("d_checkfromurl").checked=true;$("d_fromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}if(cu){$("d_checkfromfile").checked=false;$("uploadfile").disabled=true;}break;case "file":$("d_checkfromurl").checked=false;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}if(cu){$("d_checkfromfile").checked=true;$("uploadfile").disabled=false;}break;}};function UploadError(an){gd();bP('file');divProcessing.style.display="none";try{bX($("uploadfile"),md(an,config.AllowImageExt,config.AllowImageSize));}catch(e){}};function UploadSaved(gS){$("d_fromurl").value=gS;eH();};function eH(){bx=$("d_fromurl").value;dP.$("d_image").value=bx;parent.bV();};function ok(){if($("d_checkfromurl").checked){eH();return;}if(cu){if($("d_checkfromfile").checked){if(!iV($("uploadfile").value,config.AllowImageExt)){UploadError("ext");return false;}he();divProcessing.style.display="";document.myuploadform.submit();return;}}};function he(){$("d_checkfromfile").disabled=true;$("d_checkfromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}$("d_fromurl").disabled=true;$("d_ok").disabled=true;};function gd(){$("d_checkfromfile").disabled=false;$("d_checkfromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}$("d_fromurl").disabled=false;$("d_ok").disabled=false;} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td> <fieldset> <legend><span lang=DlgBkImgSource></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=0 width="300px"><tr><td></td></tr></table> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <script type="text/javascript"> if(cu){document.write("<tr>");document.write("<td noWrap width=\"20%\"><input type=radio id=\"d_checkfromfile\" value=\"1\" onclick=\"bP('file')\"><label for=d_checkfromfile>"+lang["DlgFromFile"]+"</label>:</td>");document.write("<td noWrap width=\"80%\" colspan=2>");document.write(lN("image"));document.write("</td>");document.write("</tr>");} </script> <tr> <td width="20%" noWrap><input type=radio id="d_checkfromurl" onclick="bP('url')"><label for=d_checkfromurl><span lang=DlgFromUrl></span></label>:</td> <td width="80%" noWrap colspan=2> <script type="text/javascript"> if(config.AllowBrowse=="1"){document.write("<table border=0 cellpadding=0 cellspacing=0 width='100%'><tr><td width='100%'><input type=text id='d_fromurl' style='width:100%' size=30 value=''></td><td><input class='dlgBtnBrowse' type=button id='d_browse' onclick=\"nf('image','fromurl')\" value='"+lang["DlgBtnBrowse"]+"' align=absmiddle></td></tr></table>");}else{document.write("<input type=text id='d_fromurl' style='width:100%' size=30 value=''>");} </script> </td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr><td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="d_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel></td></tr> </table> </td></tr></table> <div id=divProcessing style="width:200px;height:30px;position:absolute;left:70px;top:45px;display:none"> <table border=0 cellpadding=0 cellspacing=1 bgcolor="#000000" width="100%" height="100%"><tr><td bgcolor=#3A6EA5><marquee align="middle" behavior="alternate" scrollamount="5"><font color=#FFFFFF><span lang=DlgComUploading></span></font></marquee></td></tr></table> </div> </body> </html>