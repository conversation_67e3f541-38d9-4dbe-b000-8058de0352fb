<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aJ=cc["action"];var jy=cc["autodone"];var iB="";if(aJ=="paste"){iB="("+lang["DlgWordPaste"]+")";}else{aJ="import";}var bm=lang["DlgExcel"]+iB;document.write("<title>"+bm+"</title>");var ll=(parseInt(config.WIAPI)<10)?true:false;function ok(){if(DLGRunOne.FH("btn_ok")){return;}DLGRunOne.BP("btn_ok",true);if(!FT.FV(true)){DLGRunOne.BP("btn_ok",false);return;}var es="";var dO=0;if(aJ!="paste"){if($("d_sheet").length<=0){alert(lang["DlgExcelInvalidFile"]);DLGRunOne.BP("btn_ok",false);return;}es=$("d_file").value;dO=parseInt($("d_sheet").options[$("d_sheet").selectedIndex].value);}var eL="";if($("d_imgjpg").checked){eL="jpg";}else if($("d_imggif").checked){eL="gif";}else if($("d_imgpng").checked){eL="png";}var cw="";if(aJ!="paste"){if(ll){if($("d_api0").checked){cw="0";}else if($("d_api1").checked){cw="1";}else{cw="2";}}else{cw=config.WIAPI.substr(1);}}var bu="";bu+="api:"+cw+";";bu+="mode:"+($("d_modehtml").checked?"html":"img")+";";bu+="imgtype:"+eL+";";bu+="optimizemode:"+($("d_opt2").checked?"2":"1")+";";bu+="opt1vml:1;";bu+="opt1css:1;";bu+="opt1space:"+($("d_opt1space").checked?"1":"0")+";";bu+="opt1table:"+($("d_opt1table").checked?"1":"0")+";";bu+="opt1overflow:"+($("d_opt1overflow").checked?"1":"0")+";";bu+="opt2image:"+($("d_opt2image").checked?"1":"0")+";";bu+="opt2space:"+($("d_opt2space").checked?"1":"0")+";";bu+="opt2table:"+($("d_opt2table").checked?"1":"0")+";";$("divProcessing").style.display="";if(aJ!="paste"){FT.kI.ImportExcelSheet(es,dO,bu);}else{FT.kI.PasteExcel(bu);}window.setTimeout(hT,1000);};function hT(){if(FT.kI.Status!="ok"){window.setTimeout(hT,300);return;}if(FT.Fi()){$("divProcessing").style.display="none";DLGRunOne.BP("btn_ok",false);return;}var ak=FT.kI.Style;if($("d_opt2").checked){ak="";}var bC=FT.kI.Body;EWIN.addUploadFiles(FT.kI.OriginalFiles,FT.kI.SavedFiles);if($("d_pos").checked){EWIN.setHTML(ak+bC,true);}else{EWIN.insertHTML(bC);if(ak){var fh=ak+EWIN.getHTML();EWIN.setHTML(fh,true);}}$("divProcessing").style.display="none";parent.bV({flag:"AfterImportExcel",action:aJ});};var oR="";function vn(){if(!FT.FV(true)){return;}var es=fk($("d_file").value);if((oR!=es)){$("d_sheet").options.length=0;oR="";}if(es==""){return;}if(es.indexOf(":")<0){return;}if((oR!="")&&(oR==es)){return;}if(!iV(es,"xls|xlsx|et")){return;}var zg=FT.kI.GetExcelWorkSheetName(es);if(FT.Fi()){return;}$("d_sheet").options[0]=new Option(lang["DlgExcelSheetAll"],"0");var xi=zg.split("\n");for(var i=0;i<xi.length;i++){$("d_sheet").options[$("d_sheet").options.length]=new Option(xi[i],i+1);}oR=es;};function hX(index){var jZ,K;for(var i=1;i<=2;i++){jZ=$("group_opt"+i);K=jZ.getElementsByTagName("INPUT");for(var j=0;j<K.length;j++){if(index==i){K[j].disabled=false;}else{K[j].disabled=true;}}K=jZ.getElementsByTagName("SPAN");for(var j=0;j<K.length;j++){if(index==i){K[j].disabled=false;}else{K[j].disabled=true;}}}};function oj(flag){if(flag==1){$("tab_modehtml").style.display="";$("tab_modeimg").style.display="none";}else{if($("tab_modehtml").style.display=="none"){return;}$("tab_modeimg").style.height=$("tab_modehtml").offsetHeight;$("tab_modeimg").style.width=$("tab_modehtml").offsetWidth;$("tab_modehtml").style.display="none";$("tab_modeimg").style.display="";}};function uc(){if(jy){window.setTimeout("ok()",100);}};function aq(){lang.ag(document);if(config.WIIMode=="2"){$("d_opt2").checked=true;hX(2);}else{$("d_opt1").checked=true;hX(1);}if(aJ=="paste"){document.getElementById("d_pos").checked=false;}else{switch(config.WIAPI){case "0":case "10":bU(0);break;case "1":case "11":bU(1);break;case "2":case "12":bU(2);break;}if(ll){$("d_api"+config.WIAPI).checked=true;}}parent.ar(bm);kx();if(jy){ok();}};function kx(){var el=$("divProcessing");var dw=$("tabDialogSize").offsetWidth;var pB=$("tabDialogSize").offsetHeight;if(dw<50||pB<50){window.setTimeout("kx()",100);}el.style.left=(dw+6-parseInt(el.style.width))/2+"px";el.style.top=(pB-parseInt(el.style.height))/2+"px";};function bU(AW){if(AW==1){$("sp_modeimg").style.display="";}else{oj(1);$("d_modehtml").checked=true;$("sp_modeimg").style.display="none";}};function DO(){if(!FT.FV(true)){return;}var es=FT.kI.DialogOpen(1,0,lang["DlgExcelFile"]+"(*.xls,*.xlsx,*.et)|*.xls;*.xlsx;*.et",1,"","");if(es){$("d_file").value=es;}if(es){vn();}} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <script type="text/javascript"> if(aJ!="paste"){document.write("<tr>");document.write("	<td>");document.write("	<fieldset>");document.write("	<legend><span lang=DlgExcelLegend></span>:</legend>");document.write("	<table border=0 cellpadding=5 cellspacing=0 width='100%'>");document.write("	<tr><td>");document.write("		<table border=0 cellpadding=0 cellspacing=2 width='100%'>");document.write("		<tr>");document.write("			<td noWrap><span lang=DlgExcelFile></span>:</td>");document.write("			<td noWrap width='100%'><input type=text id='d_file' size=30 style='width:240px' onchange='vn()' onkeyup='vn()'> <input type=button class='dlgBtnCommon dlgBtn' lang=DlgBtnBrowse onclick='DO()'></td>");document.write("		</tr>");document.write("		<tr>");document.write("			<td noWrap><span lang=DlgExcelSheet></span>:</td>");document.write("			<td noWrap><select id='d_sheet' size=1 style='width:100%'></select></td>");document.write("		</tr>");if(ll){document.write("		<tr>");document.write("			<td noWrap><span lang=DlgExcelAPI></span>:</td>");document.write("			<td noWrap><input type=radio name=d_api id=d_api0 onclick='bU(0)' checked><label for=d_api0>"+lang["DlgExcelAPI0"]+"</label> <input type=radio name=d_api id=d_api1 onclick='bU(1)' checked><label for=d_api1>"+lang["DlgExcelAPI1"]+"</label> <input type=radio name=d_api id=d_api2 onclick='bU(2)'><label for=d_api2>"+lang["DlgExcelAPI2"]+"</label></td>");document.write("		</tr>");}document.write("		</table>");document.write("	</td></tr>");document.write("	</table>");document.write("	</fieldset>");document.write("	</td>");document.write("</tr>");document.write("<tr><td height=5></td></tr>");} </script> <tr> <td> <fieldset> <legend><span lang=DlgExcelOptimize></span>: <input type=radio id=d_modehtml name=g_mode checked onclick="oj(1)"><label for=d_modehtml><span lang=DlgWordModeHTML></span></label>&nbsp;<span id=sp_modeimg><input type=radio id=d_modeimg name=g_mode onclick="oj(2)"><label for=d_modeimg><span lang=DlgWordModeIMG></span></label></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=3 id=tab_modehtml> <tr><td colspan=5 noWrap><input type=radio name="d_optimize" id="d_opt1" checked onclick="hX(1)"><label for=d_opt1><span lang=DlgExcelOpt1></span></label></td></tr> <tr id=group_opt1> <td>&nbsp;&nbsp;&nbsp; </td>  <td noWrap><input type=checkbox id=d_opt1space><label for=d_opt1space><span lang=DlgExcelOpt1Space></span></label></td> <td noWrap><input type=checkbox id=d_opt1table><label for=d_opt1table><span lang=DlgExcelOpt1Table></span></label></td> <td noWrap><input type=checkbox id=d_opt1overflow><label for=d_opt1overflow><span lang=DlgExcelOpt1Overflow></span></label></td> <td></td> </tr> <tr><td colspan=5 noWrap><input type=radio name="d_optimize" id="d_opt2" onclick="hX(2)"><label for=d_opt2><span lang=DlgExcelOpt2></span></label></td></tr> <tr id=group_opt2> <td>&nbsp; </td> <td noWrap><input type=checkbox id=d_opt2image checked><label for=d_opt2image><span lang=DlgExcelOpt2Image></span></label></td> <td noWrap><input type=checkbox id=d_opt2space><label for=d_opt2space><span lang=DlgExcelOpt2Space></span></label></td> <td noWrap><input type=checkbox id=d_opt2table checked><label for=d_opt2table><span lang=DlgExcelOpt2Table></span></label></td> <td></td> </tr> </table> <table border=0 cellpadding=0 cellspacing=3 id=tab_modeimg style="display:none"> <tr> <td noWrap><span lang=DlgWordImgType></span>: <input type=radio id=d_imggif name=d_imgtype checked><label for=d_imggif>GIF</label> <input type=radio id=d_imgjpg name=d_imgtype><label for=d_imgjpg>JPG</label> <input type=radio id=d_imgpng name=d_imgtype><label for=d_imgpng>PNG</label></td> </tr> <tr><td><span lang=DlgWordImgAlt></span></td></tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <table border=0 cellpadding=0 cellspacing=0 width="100%"> <tr> <td noWrap><input type=checkbox id=d_pos checked><label for=d_pos><span lang=DlgComInsertReplace></span></label></td> <td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel> </tr> </table> </td> </tr> </table> </td></tr></table> <div id=divProcessing style="width:200px;height:30px;position:absolute;left:60px;top:85px;display:none"> <table border=0 cellpadding=0 cellspacing=1 bgcolor="#000000" width="100%" height="100%"><tr><td bgcolor=#3A6EA5 align=center><marquee align="middle" behavior="alternate" scrollamount="5"><font color=#FFFFFF><span lang=DlgExcelImporting></span></font></marquee></td></tr></table> </div> </body> </html> 