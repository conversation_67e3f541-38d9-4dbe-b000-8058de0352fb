<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var bm=lang["DlgFR"];document.write("<title>"+bm+"</title>");var fc;var dC;var Dn;var Dp;var De;if(F.as){dC=EWIN.document.selection.createRange();}else if(F.eo){dC=EWIN.document.body.createTextRange();dC.collapse(true);}else{Dn=EWEB.db.selectionStart;Dp=EWEB.db.selectionEnd;}function yq(){var vV=0;var xa=0;var vv=0;if($("d_matchcase").checked){xa=4;}if($("d_matchword").checked){vv=2;}vV=xa+vv;return(vV);};function sn(){if($("d_search").value.length<1){alert(lang["DlgFRNoKey"]);return false;}else{return true;}};function qo(){if(!sn()){return;}var mi=$("d_search").value;if(F.as||F.eo){dC.collapse(false);if(dC.findText(mi,1000000000,yq())){if(dC.parentElement()==EWEB.db){dC.select();C.Release();C.Save();}else{qo();}}else{var tS=confirm(lang["DlgFRRestart"]);if(tS){dC.expand("textedit");dC.collapse();dC.select();qo();}}}else{var ta=($("d_matchcase").checked);var uG=($("d_matchword").checked);var Dc=false;De=false;if(EWIN.find(mi,ta,false,false,uG)){if(EWEB.db.selectionEnd>EWEB.db.selectionStart){Dn=EWEB.db.selectionStart;Dp=EWEB.db.selectionEnd;De=true;return;}else{fc=EWIN.getSelection();var Df=fc.getRangeAt(0);Df.collapse(false);Dc=true;}}var tS=confirm(lang["DlgFRRestart"]);if(tS){EWEB.db.setSelectionRange(0,0);EWEB.db.focus();qo();}else{if(Dc){EWEB.db.setSelectionRange(Dn,Dp);EWEB.db.focus();}}}};function yT(){if(!sn()){return;}var lp=$("d_replace").value;if(F.as||F.eo){if(dC.text!=""){dC.text=lp;}}else{if(De){var v=EWEB.db.value;v=v.substring(0,Dn)+lp+v.substring(Dp);EWEB.db.value=v;var ni=Dn+lp.length;Dp=Dn+lp.length;EWEB.db.setSelectionRange(ni,ni);EWEB.db.focus();}}qo();};function xq(){if(!sn()){return;}var mi=$("d_search").value;var lp=$("d_replace").value;var qO=0;var sd="";if(F.as||F.eo){dC.expand("textedit");dC.collapse();dC.select();while(dC.findText(mi,1000000000,yq())){dC.select();dC.text=lp;qO++;}}else{var ta=($("d_matchcase").checked);var uG=($("d_matchword").checked);EWEB.db.setSelectionRange(0,0);EWEB.db.focus();while(EWIN.find(mi,ta,false,false,uG)){if(EWEB.db.selectionEnd>EWEB.db.selectionStart){Dn=EWEB.db.selectionStart;Dp=EWEB.db.selectionEnd;var v=EWEB.db.value;v=v.substring(0,Dn)+lp+v.substring(Dp);EWEB.db.value=v;var ni=Dn+lp.length;Dp=Dn+lp.length;EWEB.db.setSelectionRange(ni,ni);EWEB.db.focus();qO++;}else{fc=EWIN.getSelection();var Df=fc.getRangeAt(0);Df.collapse(false);break;}}}if(qO==0){sd=lang["DlgFRNoFound"]}else{sd=qO+" "+lang["DlgFRReplaceOK"];}alert(sd);};function aq(){lang.ag(document);parent.ar(bm);} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table cellspacing="0" cellpadding="0" border="0" align=center> <tr> <td valign="top" align="left" nowrap width="60%"> <label for="d_search"><span lang=DlgFRSearchKey></span></label><br> <input type=text size=25 id="d_search"><br> <label for="d_replace"><span lang=DlgFRReplaceKey></span></label><br> <input type=text size=25 id="d_replace"><br> <input type=checkbox id="d_matchcase"><label for="d_matchcase"><span lang=DlgFRMatchCase></span></label><br> <input type=checkbox id="d_matchword"><label for="d_matchword"><span lang=DlgFRMatchWord></span></label> </td> <td width="5%"> <td rowspan="2" valign="bottom" width="35%"> <table border=0 cellpadding=0 cellspacing=5 width="100%" align=center> <tr><td><input type=button class="dlgBtnFind dlgBtn" name="btnFind" onClick="qo();" value="" lang=DlgFRFindNext></td></tr> <tr><td><input type=button class="dlgBtnFind dlgBtn" name="btnCancel" onClick="parent.bn()" value="" lang=DlgBtnClose></td></tr> <tr><td><input type=button class="dlgBtnFind dlgBtn" name="btnReplace" onClick="yT();" value="" lang=DlgFRReplace></td></tr> <tr><td><input type=button class="dlgBtnFind dlgBtn" name="btnReplaceall" onClick="xq();" value="" lang=DlgFRReplaceAll></td></tr> </table> </td> </tr> </table> </td></tr></table> </body> </html> 