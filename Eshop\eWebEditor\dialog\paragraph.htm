<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var bm=lang["DlgPar"];document.write("<title>"+bm+"</title>");var mY="";var mR="";var nj="";var kt="";var jl="";var iE="";var mE="";var mG="";var ad,bB;if(F.as){ad=EWEB.T.selection.createRange();bB=EWEB.T.body.createTextRange();}else{ad=C.bG().getRangeAt(0);bB=EWEB.T.createRange();}var aX=new Array();var oSelParentParagrash=C.lF(["P"]);if(oSelParentParagrash){aX[aX.length]=oSelParentParagrash;}else{var mq=EWEB.T.body.getElementsByTagName("P");for(var i=0;i<mq.length;i++){if(F.as){bB.moveToElementText(mq[i]);if(ad.inRange(bB)){aX[aX.length]=mq[i];}else{if(((ad.compareEndPoints("StartToEnd",bB)<0)&&(ad.compareEndPoints("StartToStart",bB)>0))||((ad.compareEndPoints("EndToStart",bB)>0)&&(ad.compareEndPoints("EndToEnd",bB)<0))){aX[aX.length]=mq[i];}}}else{bB.selectNodeContents(mq[i]);if((bB.compareBoundaryPoints(Range.START_TO_END,ad)>=0)&&(bB.compareBoundaryPoints(Range.END_TO_START,ad)<=0)){aX[aX.length]=mq[i];}}}}for(var i=0;i<aX.length;i++){if(i==0){mY=aX[i].style.marginTop;mR=aX[i].style.marginBottom;nj=aX[i].style.marginLeft;kt=aX[i].style.marginRight;jl=aX[i].style.textAlign;iE=aX[i].style.lineHeight;mE=aX[i].style.textIndent;mG=aX[i].style.letterSpacing;}else{if(aX[i].style.marginTop!=mY){mY="";}if(aX[i].style.marginBottom!=mR){mR="";}if(aX[i].style.marginLeft!=nj){nj="";}if(aX[i].style.marginRight!=kt){kt="";}if(aX[i].style.textAlign!=jl){jl="";}if(aX[i].style.lineHeight!=iE){iE="";}if(aX[i].style.textIndent!=mE){mE="";}if(aX[i].style.letterSpacing!=mG){mG="";}}}function xK(){var xk=$("d_lineheightdrop").options[$("d_lineheightdrop").selectedIndex].value;if(xk=="other"){return;}else{$("d_lineheight").value=xk;}};function yJ(){var bv=fk($("d_lineheight").value);switch(bv){case "":$("d_lineheightdrop").selectedIndex=1;break;case "1":$("d_lineheightdrop").selectedIndex=2;break;case "1.5":$("d_lineheightdrop").selectedIndex=3;break;case "2":$("d_lineheightdrop").selectedIndex=4;break;default:$("d_lineheightdrop").selectedIndex=0;break;}};function aq(){lang.ag(document);aC($("d_align"),jl.toLowerCase());aC($("d_lineheightdrop"),iE);$("d_margintop").value=mY;$("d_marginbottom").value=mR;$("d_marginleft").value=nj;$("d_marginright").value=kt;$("d_lineheight").value=iE;$("d_textindent").value=mE;$("d_letterspacing").value=mG;parent.ar(bm);};function ok(){mY=$("d_margintop").value;mR=$("d_marginbottom").value;nj=$("d_marginleft").value;kt=$("d_marginright").value;jl=$("d_align").options[$("d_align").selectedIndex].value;iE=$("d_lineheight").value;mE=$("d_textindent").value;mG=$("d_letterspacing").value;for(var i=0;i<aX.length;i++){try{aX[i].style.marginTop=mY}catch(e){};try{aX[i].style.marginBottom=mR}catch(e){};try{aX[i].style.marginLeft=nj}catch(e){};try{aX[i].style.marginRight=kt}catch(e){};try{aX[i].style.textAlign=jl;if(jl=="justify"){aX[i].style.textJustify="inter-ideograph";}else{aX[i].style.textJustify="";}}catch(e){};try{aX[i].style.lineHeight=iE}catch(e){};try{aX[i].style.textIndent=mE}catch(e){};try{aX[i].style.letterSpacing=mG}catch(e){};}parent.bV();} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td> <fieldset> <legend><span lang=DlgPraMargin></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgPraMarginTop></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_margintop size=10 value=""></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgPraMarginBottom></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_marginbottom size=10 value=""></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgPraMarginLeft></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_marginleft size=10 value=""></td> <td width="2%"></td> <td noWrap width="20%"><span lang=DlgPraMarginRight></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_marginright size=10 value=""></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr> <td> <fieldset> <legend><span lang=DlgPraOther></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgAlign></span>:</td> <td noWrap width="29%"> <select id=d_align size=1 style="width:80px"> <option value='' selected lang=DlgComDefault></option> <option value='left' lang=DlgAlignLeft></option> <option value='right' lang=DlgAlignRight></option> <option value='center' lang=DlgAlignCenter></option> <option value='justify' lang=DlgAlignFull></option> </select> </td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgPraLineHeight></span>:</td> <td noWrap width="29%"> <div style="position:relative;"> <span style="margin-left:62px;width:18px;overflow:hidden;"> <select id=d_lineheightdrop style="width:80px;margin-left:-62px" onchange="xK()"> <option value="other" lang=DlgPraLHOther></option> <option value="" lang=DlgComDefault></option> <option value="1" lang=DlgPraLH1></option> <option value="1.5" lang=DlgPraLH15></option> <option value="2" lang=DlgPraLH2></option> </select> </span> <input style="width:62px;position:absolute;left:0px;" id=d_lineheight size=10 value="" onchange="yJ()"> </div> </td> </tr> <tr> <td noWrap width="20%"><span lang=DlgPraTextIndent></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_textindent size=10 value=""></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgPraWordSpacing></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_letterspacing size=10 value=""></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr><td align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel></td></tr> </table> </td></tr></table> </body></html> 