<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var bm=lang["DlgPPT"];document.write("<title>"+bm+"</title>");var ll=(parseInt(config.WIAPI)<10)?true:false;var FE=(((config.SYWZFlag=="2"||config.SYTPFlag=="2")&&config.SYVLocal=="1")?true:false);function ok(){if(DLGRunOne.FH("btn_ok")){return;}DLGRunOne.BP("btn_ok",true);if(!FT.FV(true)){DLGRunOne.BP("btn_ok",false);return;}var es=$("d_file").value;if(!iV(es,"ppt|pptx|dps")){alert(lang["DlgPPTInvalidFile"]);DLGRunOne.BP("btn_ok",false);return;}var eL=$("d_imgtype").options[$("d_imgtype").selectedIndex].value;var cw="";if(ll){if($("d_api0").checked){cw="0";}else if($("d_api1").checked){cw="1";}else{cw="2";}}else{cw=config.WIAPI.substr(1);}var bu="";bu+="api:"+cw+";";bu+="imgtype:"+eL+";";bu+="tw:"+$("d_tw").value+";";$("divProcessing").style.display="";if(FE){if($("d_syflag").checked){FT.kI.SendUrl=EWEB.SendUrl+"&syflag=1";}}FT.kI.ImportPPT(es,bu);window.setTimeout(hT,1000);};function hT(){if(FT.kI.Status!="ok"){window.setTimeout(hT,300);return;}if(FT.Fi()){$("divProcessing").style.display="none";DLGRunOne.BP("btn_ok",false);return;}var ak="";var bC=FT.kI.Body;EWIN.addUploadFiles(FT.kI.OriginalFiles,FT.kI.SavedFiles);if($("d_imgefflag").checked){bC='<div class="ewebeditor_doc" style="width:'+$('d_imgefwidth').value+';height:'+$('d_imgefheight').value+'; overflow:auto;background-color:#A0A0A3;border:1px solid #D4D0C8;text-align:center;">'+bC.replace(/(<img)(\s[^>]*?>)/gi,'$1 style="border-width:1px 2px 2px 1px;border-color:#00000;margin:5px;"$2')+'</div>';}if($("d_pos").checked){EWIN.setHTML(ak+bC,true);}else{EWIN.insertHTML(bC);if(ak){var fh=ak+EWIN.getHTML();EWIN.setHTML(fh,true);}}$("divProcessing").style.display="none";parent.bV({flag:"AfterImportPPT",action:""});};function pf(bd,gK){pz(bd,gK,"INPUT");pz(bd,gK,"SPAN");};function pz(bd,gK,aH){var K=bd.getElementsByTagName(aH);for(var j=0;j<K.length;j++){K[j].disabled=gK;}};function pK(){var b=$("d_imgefflag").checked;pf($("group_imgef"),!b);};function Bi(){var b=$("d_twflag").checked;pf($("group_tw"),!b);};function aq(){lang.ag(document);switch(config.WIAPI){case "0":case "10":bU(0);break;case "1":case "11":bU(1);break;case "2":case "12":bU(2);break;}if(ll){$("d_api"+config.WIAPI).checked=true;}Bi();pK();parent.ar(bm);kx();};function kx(){var el=$("divProcessing");var dw=$("tabDialogSize").offsetWidth;var pB=$("tabDialogSize").offsetHeight;if(dw<50||pB<50){window.setTimeout("kx()",100);}el.style.left=(dw+6-parseInt(el.style.width))/2+"px";el.style.top=(pB-parseInt(el.style.height))/2+"px";};function bU(AW){};function DO(){if(!FT.FV(true)){return;}var es=FT.kI.DialogOpen(1,0,lang["DlgPPTFile"]+"(*.ppt,*.pptx,*.dps)|*.ppt;*.pptx;*.dps",1,"","");if(es){$("d_file").value=es;}} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td> <fieldset> <legend><span lang=DlgPPTLegend></span>:</legend> <table border=0 cellpadding=5 cellspacing=0 width='100%'><tr><td> <table border=0 cellpadding=0 cellspacing=2 width='100%'> <tr> <td noWrap><span lang=DlgPPTFile></span>:</td> <td noWrap width='100%'><input type=text id='d_file' size=30 style='width:255px'> <input type=button class='dlgBtnCommon dlgBtn' lang=DlgBtnBrowse onclick='DO()'></td> </tr> <script type="text/javascript"> if(ll){document.write("		<tr>");document.write("			<td noWrap><span lang=DlgPPTAPI></span>:</td>");document.write("			<td noWrap><input type=radio name=d_api id=d_api0 onclick='bU(0)' checked><label for=d_api0>"+lang["DlgPPTAPI0"]+"</label>&nbsp; <input type=radio name=d_api id=d_api1 onclick='bU(1)' checked><label for=d_api1>"+lang["DlgPPTAPI1"]+"</label>&nbsp; <input type=radio name=d_api id=d_api2 onclick='bU(2)'><label for=d_api2>"+lang["DlgPPTAPI2"]+"</label></td>");document.write("		</tr>");} </script> </table> </td></tr></table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <fieldset> <legend><span lang=DlgPPTOptimize></span>:</legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td valign=top> <table border=0 cellpadding=0 cellspacing=3> <tr> <td noWrap width="20%"><span lang=DlgWordImgType></span>:</td> <td noWrap width="29%"> <select id=d_imgtype size=1 style="width:80px"> <option value='jpg'>JPG</option> <option value='png' selected>PNG</option> </select> </td> <td width="2%">&nbsp;</td> <td noWrap width="20%"></td> <td noWrap width="29%"></td> </tr> <script type="text/javascript"> if(FE){document.write("<tr><td><span lang=DlgImgSYFlag></span>:</td><td colspan=4><input type=checkbox id=d_syflag value='1'></td></tr>");} </script> <tr><td colspan=5><input type=checkbox id=d_twflag value='1' onclick='Bi()' checked><label for=d_twflag><span lang=DlgPPTtwFlag></span>:</label> <span id=group_tw><span lang=DlgPPTtw></span><input type=text size=5 id=d_tw style='width:50px' value='600' onkeydown="eR(event);">&nbsp;<span lang=DlgPPTth></span></span></td></tr> <tr><td colspan=5><input type=checkbox id=d_imgefflag value='1' onclick='pK()' checked><label for=d_imgefflag><span lang=DlgWordImgEfFlag></span>:</label> <span id=group_imgef><span lang=DlgWordImgEfWidth></span><input type=text size=5 id=d_imgefwidth style='width:50px' value='100%'>&nbsp;<span lang=DlgWordImgEfHeight></span><input type=text size=5 id=d_imgefheight style='width:50px' value=''></span></td></tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <table border=0 cellpadding=0 cellspacing=0 width="100%"> <tr> <td noWrap><input type=checkbox id=d_pos checked><label for=d_pos><span lang=DlgComInsertReplace></span></label></td> <td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel> </tr> </table> </td> </tr> </table> </td></tr></table> <div id=divProcessing style="width:200px;height:30px;position:absolute;left:50px;top:30px;display:none"> <table border=0 cellpadding=0 cellspacing=1 bgcolor="#000000" width="100%" height="100%"><tr><td bgcolor=#3A6EA5 align=center><marquee align="middle" behavior="alternate" scrollamount="5"><font color=#FFFFFF><span lang=DlgPPTImporting></span></font></marquee></td></tr></table> </div> </body> </html> 