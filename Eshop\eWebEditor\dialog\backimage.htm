<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aJ="";var aa="";var t;var bx="";var dH="";var cS="";var et="sys";var cu=((parseFloat(config.AllowImageSize)>0)?true:false);if(cc['action']=="other"){aJ="OTHER";aa=lang["DlgComSet"];bx=dP.$("d_image").value;dH=dP.$("d_repeat").value;cS=dP.$("d_attachment").value;et="url";}else{aJ="INSERT";aa=lang["DlgComBody"];if(C.ai()=="Control"){t=C.ax();}else{t=C.cI();}t=zs(t);if(t){switch(t.tagName){case "TD":aa=lang["DlgComTableCell"];break;case "TR":case "TH":aa=lang["DlgComTableRow"];break;default:aa=lang["DlgComTable"];break;}aJ="MODI";bx=t.style.backgroundImage;dH=t.style.backgroundRepeat;cS=t.style.backgroundAttachment;et="url";bx=bx.replace(/\"/gi,"");bx=bx.substr(4,bx.length-5);}}var bm=lang["DlgBkImg"]+"("+aa+")";document.write("<title>"+bm+"</title>");function aq(){lang.ag(document);aC($("d_repeat"),dH.toLowerCase());aC($("d_attachment"),cS.toLowerCase());if(bx){for(var i=0;i<$("d_fromsys").options.length;i++){if(gh("sysimage/bg/"+$("d_fromsys").options[i].value)==bx){bx="";$("d_fromsys").selectedIndex=i;et="sys";break;}}}$("d_fromurl").value=bx;if((!cu)&&(et=="file")){et="sys";}bP(et);parent.ar(bm);};function zs(obj){while(obj!=null&&obj.tagName!="TD"&&obj.tagName!="TR"&&obj.tagName!="TH"&&obj.tagName!="TABLE"){obj=obj.parentNode;}return obj;};function bP(an){switch(an){case "url":$("d_checkfromurl").checked=true;$("d_checkfromsys").checked=false;$("d_checkcancel").checked=false;$("d_fromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}$("d_fromsys").disabled=true;if(cu){$("d_checkfromfile").checked=false;$("uploadfile").disabled=true;}break;case "file":$("d_checkfromurl").checked=false;$("d_checkfromsys").checked=false;$("d_checkcancel").checked=false;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}$("d_fromsys").disabled=true;if(cu){$("d_checkfromfile").checked=true;$("uploadfile").disabled=false;}break;case "sys":$("d_checkfromurl").checked=false;$("d_checkfromsys").checked=true;$("d_checkcancel").checked=false;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}$("d_fromsys").disabled=false;if(cu){$("d_checkfromfile").checked=false;$("uploadfile").disabled=true;}break;case "cancel":$("d_checkfromurl").checked=false;$("d_checkfromsys").checked=false;$("d_checkcancel").checked=true;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}$("d_fromsys").disabled=true;if(cu){$("d_checkfromfile").checked=false;$("uploadfile").disabled=true;}break;}};function UploadError(an){gd();bP('file');divProcessing.style.display="none";try{bX($("uploadfile"),md(an,config.AllowImageExt,config.AllowImageSize));}catch(e){}};function UploadSaved(gS){$("d_fromurl").value=gS;eH();};function eH(){if($("d_fromurl").value==""){bx="";dH="";cS="";}else{if(aJ=="OTHER"){bx=$("d_fromurl").value;}else{bx="url("+$("d_fromurl").value+")";}dH=$("d_repeat").options[$("d_repeat").selectedIndex].value;cS=$("d_attachment").options[$("d_attachment").selectedIndex].value;}switch(aJ){case "MODI":t.style.backgroundImage=bx;t.style.backgroundRepeat=dH;t.style.backgroundAttachment=cS;break;case "OTHER":dP.$("d_image").value=bx;dP.$("d_repeat").value=dH;dP.$("d_attachment").value=cS;break;default:EWIN.setHTML("<table border=0 cellpadding=0 cellspacing=0 width='100%' height='100%'><tr><td valign=top id='eWebEditor_TempElement_Background'>"+EWIN.getHTML()+"</td></tr></table>",true);var hG=EWEB.T.getElementById("eWebEditor_TempElement_Background");hG.style.backgroundImage=bx;hG.style.backgroundRepeat=dH;hG.style.backgroundAttachment=cS;hG.removeAttribute("id");break;}parent.bV();};function ok(){if($("d_checkfromurl").checked){eH();return;}if(cu){if($("d_checkfromfile").checked){if(!iV($("uploadfile").value,config.AllowImageExt)){UploadError("ext");return false;}he();divProcessing.style.display="";document.myuploadform.submit();return;}}if($("d_checkfromsys").checked){$("d_fromurl").value=gh("sysimage/bg/"+$("d_fromsys").options[$("d_fromsys").selectedIndex].value);eH();return;}if($("d_checkcancel").checked){$("d_fromurl").value="";eH();return;}};function he(){$("d_checkfromfile").disabled=true;$("d_checkfromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}$("d_checkfromsys").disabled=true;$("d_checkcancel").disabled=true;$("d_fromurl").disabled=true;$("d_fromsys").disabled=true;$("d_repeat").disabled=true;$("d_attachment").disabled=true;$("d_ok").disabled=true;};function gd(){$("d_checkfromfile").disabled=false;$("d_checkfromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}$("d_checkfromsys").disabled=false;$("d_checkcancel").disabled=false;$("d_fromurl").disabled=false;$("d_fromsys").disabled=false;$("d_repeat").disabled=false;$("d_attachment").disabled=false;$("d_ok").disabled=false;} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td> <fieldset> <legend><span lang=DlgBkImgSource></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=0 width="300px"><tr><td></td></tr></table> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <script type="text/javascript"> if(cu){document.write("<tr>");document.write("<td noWrap width=\"20%\"><input type=radio id=\"d_checkfromfile\" value=\"1\" onclick=\"bP('file')\"><label for=d_checkfromfile>"+lang["DlgFromFile"]+"</label>:</td>");document.write("<td noWrap width=\"80%\" colspan=2>");document.write(lN("image"));document.write("</td>");document.write("</tr>");} </script> <tr> <td width="20%" noWrap><input type=radio id="d_checkfromurl" onclick="bP('url')"><label for=d_checkfromurl><span lang=DlgFromUrl></span></label>:</td> <td width="80%" noWrap colspan=2> <script type="text/javascript"> if(config.AllowBrowse=="1"){document.write("<table border=0 cellpadding=0 cellspacing=0 width='100%'><tr><td width='100%'><input type=text id='d_fromurl' style='width:100%' size=30 value=''></td><td><input class='dlgBtnBrowse' type=button id='d_browse' onclick=\"nf('image','fromurl')\" value='"+lang["DlgBtnBrowse"]+"' align=absmiddle></td></tr></table>");}else{document.write("<input type=text id='d_fromurl' style='width:100%' size=30 value=''>");} </script> </td> </tr> <tr> <td width="20%" noWrap><input type=radio id="d_checkfromsys" onclick="bP('sys')"><label for=d_checkfromsys><span lang=DlgFromSys></span></label>:</td> <td noWrap> <select id="d_fromsys" size=1 style="width:80px"> <option value="snow.gif" lang=DlgBkImgSnow selected></option> <option value="nature.jpg" lang=DlgBkImgNature></option> <option value="clear.jpg" lang=DlgBkImgClear></option> <option value="glacier.jpg" lang=DlgBkImgGlacier></option> <option value="fiesta.jpg" lang=DlgBkImgFiesta></option> <option value="birthday.gif" lang=DlgBkImgBirthday></option> <option value="citrus.gif" lang=DlgBkImgCitrus></option> <option value="hearts.gif" lang=DlgBkImgHearts></option> <option value="flower.gif" lang=DlgBkImgFlower></option> <option value="gathering.jpg" lang=DlgBkImgGathering></option> <option value="christmas.gif" lang=DlgBkImgChristmas></option> <option value="ivy.gif" lang=DlgBkImgIvy></option> <option value="tech.gif" lang=DlgBkImgTech></option> <option value="maize.jpg" lang=DlgBkImgMaize></option> <option value="grid.gif" lang=DlgBkImgGrid></option> </select> </td> <td noWrap><input type=radio id="d_checkcancel" onclick="bP('cancel')"><label for=d_checkcancel><span lang=DlgBkImgCancelBg></span></label> </td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <fieldset> <legend><span lang=DlgBkImgEffect></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td width="20%" noWrap><span lang=DlgBkImgRepeat></span>:</td> <td width="29%" noWrap> <select id=d_repeat size=1 style="width:80px"> <option value='' lang=DlgComDefault selected></option> <option value='repeat' lang=DlgBkImgRepeatR></option> <option value='no-repeat' lang=DlgBkImgRepeatNo></option> <option value='repeat-x' lang=DlgBkImgRepeatX></option> <option value='repeat-y' lang=DlgBkImgRepeatY></option> </select> </td> <td width="2%">&nbsp;</td> <td width="20%" noWrap><span lang=DlgBkImgAttach></span>:</td> <td width="29%" noWrap> <select id=d_attachment size=1 style="width:80px"> <option value='' lang=DlgComDefault selected></option> <option value='scroll' lang=DlgBkImgAttachScroll></option> <option value='fixed' lang=DlgBkImgAttachFixed></option> </select> </td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr><td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="d_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel></td></tr> </table> </td></tr></table> <div id=divProcessing style="width:200px;height:30px;position:absolute;left:70px;top:45px;display:none"> <table border=0 cellpadding=0 cellspacing=1 bgcolor="#000000" width="100%" height="100%"><tr><td bgcolor=#3A6EA5><marquee align="middle" behavior="alternate" scrollamount="5"><font color=#FFFFFF><span lang=DlgComUploading></span></font></marquee></td></tr></table> </div> </body> </html>