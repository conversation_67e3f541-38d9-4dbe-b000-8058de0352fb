<?xml version="1.0" encoding="utf-8"?>

<!--

***********************************************************************************************
website.publishproj

警告: 请勿修改此文件，它将用于 Web 发布过程。

版权所有 (C) Microsoft Corporation。保留所有权利。

***********************************************************************************************
-->

<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.30319</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{721df7e9-f755-4a03-b5a6-9d275c3fa0f6}</ProjectGuid>
    <SourceWebPhysicalPath>$(MSBuildThisFileDirectory)</SourceWebPhysicalPath>
    <SourceWebVirtualPath>/Eshop</SourceWebVirtualPath>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <SourceWebProject>http://localhost:3196</SourceWebProject>
    <SourceWebMetabasePath>/IISExpress/7.5/LM/W3SVC/2/ROOT</SourceWebMetabasePath>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <!-- for VS2010 we need to use 10.5 but for VS2012+ we should use VisualStudioVersion -->
    <WebPublishTargetsVersion Condition=" '$(WebPublishTargetsVersion)' =='' and '$(VisualStudioVersion)' == 10.0 ">10.5</WebPublishTargetsVersion>
    <WebPublishTargetsVersion Condition=" '$(WebPublishTargetsVersion)'=='' ">$(VisualStudioVersion)</WebPublishTargetsVersion>

    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(WebPublishTargetsVersion)</VSToolsPath>
    <_WebPublishTargetsPath Condition=" '$(_WebPublishTargetsPath)'=='' ">$(VSToolsPath)</_WebPublishTargetsPath>
    <AssemblyFileVersion Condition="'$(AssemblyFileVersion)' == ''">*******</AssemblyFileVersion>
    <AssemblyVersion Condition="'$(AssemblyVersion)' == ''">*******</AssemblyVersion>
  </PropertyGroup>
  <ItemGroup>
    <AssemblyAttributes Include="AssemblyFileVersion">
      <Value>$(AssemblyFileVersion)</Value>
    </AssemblyAttributes>
    <AssemblyAttributes Include="AssemblyVersion">
      <Value>$(AssemblyVersion)</Value>
    </AssemblyAttributes>
  </ItemGroup>
  <Import Project="$(_WebPublishTargetsPath)\Web\Microsoft.WebSite.Publishing.targets" />
</Project>