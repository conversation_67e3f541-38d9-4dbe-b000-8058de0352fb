<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aJ=cc['action'];var aa="";var t;var cD="";var qB="";var bO="";var bD="";var dr="#000000";var bj="#FFFFFF";var bx="";var dH="";var cS="";var hC="";var gW="%";var kW=true;var gP=false;var fb="100";var gH="%";var ke=false;var jw=true;var eb="";if(aJ=="row"){t=na(C.cI(),"TR");aJ="ROW";aa=lang["DlgComTableRow"];}else{t=na(C.cI(),"TD");aJ="CELL";aa=lang["DlgComTableCell"];}if(t){cD=eZ(t,"text-align");qB=eZ(t,"vertical-align");bO=eZ(t,"width");bD=eZ(t,"height");dr=eZ(t,"borderColor");bj=eZ(t,"backgroundColor");bx=t.style.backgroundImage;dH=t.style.backgroundRepeat;cS=t.style.backgroundAttachment;hC=t.style.borderStyle;bx=bx.replace(/\"/gi,"");bx=bx.substr(4,bx.length-5);}function na(obj,iI){if(iI=="TD"){while(obj!=null&&obj.tagName!=iI&&obj.tagName!="TH"){obj=obj.parentNode;if(!obj|| !obj.tagName){obj=null;break;}}}else{while(obj!=null&&obj.tagName!=iI){obj=obj.parentNode;if(!obj|| !obj.tagName){obj=null;break;}}}return obj;};var bm=aa;document.write("<title>"+bm+"</title>");function aq(){lang.ag(document);aC($("d_align"),cD.toLowerCase());aC($("d_valign"),qB.toLowerCase());aC($("d_borderstyle"),hC.toLowerCase());if(bO==""){kW=false;gP=true;fb="100";gW="%";}else{kW=true;gP=false;if(bO.substr(bO.length-1)=="%"){fb=bO.substring(0,bO.length-1);gW="%";}else{gW="";fb=parseInt(bO);if(isNaN(fb))fb="";}}if(bD==""){ke=false;jw=true;eb="100";gH="%";}else{ke=true;jw=false;if(bD.substr(bD.length-1)=="%"){eb=bD.substring(0,bD.length-1);gH="%";}else{gH="";eb=parseInt(bD);if(isNaN(eb))eb="";}}switch(gW){case "%":$("d_widthunit").selectedIndex=1;break;default:gW="";$("d_widthunit").selectedIndex=0;break;}switch(gH){case "%":$("d_heightunit").selectedIndex=1;break;default:gH="";$("d_heightunit").selectedIndex=0;break;}$("d_widthvalue").value=fb;$("d_widthvalue").disabled=gP;$("d_widthunit").disabled=gP;$("d_heightvalue").value=eb;$("d_heightvalue").disabled=jw;$("d_heightunit").disabled=jw;$("d_bordercolor").value=dr;$("s_bordercolor").style.backgroundColor=dr;$("d_bgcolor").value=bj;$("s_bgcolor").style.backgroundColor=bj;$("d_widthcheck").checked=kW;$("d_heightcheck").checked=ke;$("d_image").value=bx;$("d_repeat").value=dH;$("d_attachment").value=cS;parent.ar(bm);};function fd(obj,kR){var b=false;if(obj.value!=""){obj.value=parseFloat(obj.value);if(obj.value!="0"){b=true;}}if(b==false){bX(obj,kR);return false;}return true;};function ok(){dr=$("d_bordercolor").value;bj=$("d_bgcolor").value;var bO="";if($("d_widthcheck").checked){if(!fd($("d_widthvalue"),lang["DlgTabCelErrWidth"]))return;bO=$("d_widthvalue").value+$("d_widthunit").value;}var bD="";if($("d_heightcheck").checked){if(!fd($("d_heightvalue"),lang["DlgTabCelErrHeight"]))return;bD=$("d_heightvalue").value+$("d_heightunit").value;}cD=$("d_align").options[$("d_align").selectedIndex].value;qB=$("d_valign").options[$("d_valign").selectedIndex].value;bx=$("d_image").value;dH=$("d_repeat").value;cS=$("d_attachment").value;hC=$("d_borderstyle").options[$("d_borderstyle").selectedIndex].value;if(bx!=""){bx="url("+bx+")";}if(t){t.style.width=bO;bq(t,"width","");t.style.height=bD;bq(t,"height","");t.style.textAlign=cD;bq(t,"align","");t.style.verticalAlign=qB;bq(t,"vAlign","");t.style.borderColor=dr;bq(t,"borderColor","");t.style.backgroundColor=bj;bq(t,"bgColor","");t.style.backgroundImage=bx;t.style.backgroundRepeat=dH;t.style.backgroundAttachment=cS;t.style.borderStyle=hC;}parent.bV();} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td> <fieldset> <legend><span lang=DlgTabCelLayout></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgTabCelHAlign></span>:</td> <td noWrap width="29%"> <select id="d_align" style="width:80px"> <option value='' lang=DlgComDefault></option> <option value='left' lang=DlgAlignLeft></option> <option value='right' lang=DlgAlignRight></option> <option value='center' lang=DlgAlignCenter></option> </select> </td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgTabCelVAlign></span>:</td> <td noWrap width="29%"> <select id="d_valign" style="width:80px"> <option value='' lang=DlgComDefault></option> <option value='top' lang=DlgAlignTop></option> <option value='middle' lang=DlgAlignMiddle></option> <option value='baseline' lang=DlgAlignBaseline></option> <option value='bottom' lang=DlgAlignBottom></option> </select> </td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <fieldset> <legend><span lang=DlgTabCelSize></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap><input id="d_widthcheck" type="checkbox" onclick="d_widthvalue.disabled=(!this.checked);d_widthunit.disabled=(!this.checked);" value="1"> <label for=d_widthcheck><span lang=DlgTabCelChkWidth></span></label></td> <td noWrap align=right> <input id="d_widthvalue" type="text" value="" size="5"> <select id="d_widthunit" size=1 style="width:65px"> <option value='px' lang=DlgComPx></option><option value='%' lang=DlgComPer></option> </select> </td> </tr> <tr> <td noWrap><input id="d_heightcheck" type="checkbox" onclick="d_heightvalue.disabled=(!this.checked);d_heightunit.disabled=(!this.checked);" value="1"> <label for=d_heightcheck><span lang=DlgTabCelChkHeight></span></label></td> <td noWrap align=right> <input id="d_heightvalue" type="text" value="" size="5"> <select id="d_heightunit" size=1 style="width:65px"> <option value='px' lang=DlgComPx></option><option value='%' lang=DlgComPer></option> </select> </td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <fieldset> <legend><span lang=DlgTabCelStyle></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgColorBorder></span>:</td> <td noWrap width="29%"><input style="width:62px" type=text id=d_bordercolor size=7 value=""><img border=0 src="images/rect.gif" width=18 style="cursor:hand" id=s_bordercolor onclick="hu('bordercolor')" align=absmiddle></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgTabCelBorderStyle></span>:</td> <td noWrap width="29%"> <select id=d_borderstyle size=1 style="width:80px"> <option value="" lang=DlgComDefault></option> <option value="solid" lang=DlgLineSolid></option> <option value="dotted" lang=DlgLineDotted></option> <option value="dashed" lang=DlgLineDashed></option> <option value="double" lang=DlgLineDouble></option> <option value="groove" lang=DlgLineGroove></option> <option value="ridge" lang=DlgLineRidge></option> <option value="inset" lang=DlgLineInset></option> <option value="outset" lang=DlgLineOutset></option> </select> </td> </tr> <tr> <td noWrap width="20%"><span lang=DlgColorBg></span>:</td> <td noWrap width="29%"><input style="width:62px" type=text id=d_bgcolor size=7 value=""><img border=0 src="images/rect.gif" width=18 style="cursor:hand" id=s_bgcolor onclick="hu('bgcolor')" align=absmiddle></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgTabCelBgImage></span>:</td> <td noWrap width="29%"><input style="width:62px" type=text id=d_image size=7 value=""><input type=hidden id=d_repeat><input type=hidden id=d_attachment><img border=0 src="images/rectimg.gif" width=18 style="cursor:hand" id=s_bgimage onclick="vo()" align=absmiddle></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr><td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel></td></tr> </table> </td></tr></table> </body> </html>