﻿/*!
* cxCalendar 1.2
* http://code.ciaoca.com/
* https://github.com/ciaoca/cxCalendar
* E-mail: <EMAIL>
* Released under the MIT license
* Date: 2013-07-31
*/
!function (a) { a.fn.cxCalendar = function (b, c) { var d, e, f, g, h, i, j, k, m, n, o, p, q, r, s, t, u, v, w, x, y, z; if (!(this.length < 1)) { for (d = {}, e = d.jqobj = this, f = d.fun = {}, b = a.extend({}, a.cxCalendar.defaults, b, { beginyear: e.data("beginyear"), endyear: e.data("endyear"), type: e.data("type"), hyphen: e.data("hyphen"), wday: e.data("wday") }), c = a.extend({}, a.cxCalendar.language, c), h = function (a) { var b = a; return b = b.replace(/\./g, "/"), b = b.replace(/-/g, "/"), b = b.replace(/\//g, "/"), b = Date.parse(b) }, i = function (a) { return 0 == a % 4 && 0 != a % 100 || 0 == a % 400 ? 1 : 0 }, e.val().length > 0 && (b.date = h(e.val())), b.date = new Date(b.date), isNaN(b.date.getFullYear()) && (b.date = new Date), b.date.setHours(0), b.date.setMinutes(0), b.date.setSeconds(0), j = b.date.getFullYear(), k = b.date.getMonth() + 1, b.date.getDate(), m = new Array(31, 28 + i(j), 31, 30, 31, 30, 31, 31, 30, 31, 30, 31), n = c.weekList, o = 6 - b.wday, p = 7 - b.wday >= 7 ? 0 : 7 - b.wday, q = a("<div></div>", { "class": "cxcalendar" }), r = a("<div></div>", { "class": "date_hd" }).appendTo(q), t = a("<table></table>").appendTo(q), r.html("<a class='date_pre' href='javascript://' rel='pre'>&lt;</a><a class='date_next' href='javascript://' rel='next'>&gt;</a>"), w = a("<div></div>", { "class": "date_txt" }).appendTo(r), s = a("<div></div>", { "class": "date_set" }).appendTo(r), v = "", z = b.beginyear; z <= b.endyear; z++) v += "<option value='" + z + "'>" + z + "</option>"; for (x = a("<select></select>", { "class": "year_set" }).html(v).appendTo(s).val(j), s.append(" - "), v = "", z = 0; 12 > z; z++) v += "<option value='" + (z + 1) + "'>" + c.monthList[z] + "</option>"; for (y = a("<select></select>", { "class": "month_set" }).html(v).appendTo(s).val(k), v = "<thead><tr>", z = 0; 7 > z; z++) v += "<th class='", z == o ? v += " sat" : z == p && (v += " sun"), v += "'>", v += z + b.wday < 7 ? n[z + b.wday] : n[z + b.wday - 7], v += "</th>"; return v += "</tr></thead>", v += "<tbody></tbody>", t.html(v), q.appendTo("body"), u = a("<div></div>", { "class": "cxcalendar_lock" }).appendTo("body"), f.show = function () { var a = document.body.clientWidth, b = document.body.clientHeight, d = q.outerWidth(), f = q.outerHeight(), g = e.offset().top, h = e.offset().left, i = e.outerWidth(), j = e.outerHeight(); return g = g + f + j > b ? g - f : g + j, h = h + d > a ? h - (d - i) : h, w.html("<span class='y'>" + x.val() + "</span>" + c.year + "<span class='m'>" + c.monthList[y.val() - 1] + "</span>" + c.month), q.css({ top: g, left: h }).show(), u.css({ width: a, height: b }).show(), this }, f.hide = function () { return q.hide(), u.hide(), s.hide(), w.show(), this }, f.change = function (a, d) { var f, g, j, k, l, n, q, r, s, u, z, A; for (1 > d ? (a--, d = 12) : d > 12 && (a++, d = 1), f = d, d--, a < b.beginyear ? a = b.endyear : a > b.endyear && (a = b.beginyear), m[1] = 28 + i(a), v = "", g = new Date(a, d, 1), j = new Date, j.setHours(0), j.setMinutes(0), j.setSeconds(0), k = new Date, k.setHours(0), k.setMinutes(0), k.setSeconds(0), k = h(e.val()), k = new Date(k), isNaN(k.getFullYear()) && (k = null), l = g.getDay() - b.wday < 0 ? g.getDay() - b.wday + 7 : g.getDay() - b.wday, n = Math.ceil((m[d] + l) / 7), z = 0; n > z; z++) { for (v += "<tr>", A = 0; 7 > A; A++) q = 7 * z + A, r = q - l + 1, r = 0 >= r || r > m[d] ? "" : q - l + 1, v += "<td", "number" == typeof r && (s = null, u = null, g = new Date(a, d, r), s = Date.parse(j) - Date.parse(g), u = Date.parse(k) - Date.parse(g), v += " title='" + a + b.hyphen + f + b.hyphen + r + "' class='num", 0 == u ? v += " selected" : 0 == s ? v += " now" : A == o ? v += " sat" : A == p && (v += " sun"), v += "'"), v += " data-day='" + r + "'>" + r + "</td>"; v += "</tr>" } return t.find("tbody").html(v), w.html("<span class='y'>" + a + "</span>" + c.year + "<span class='m'>" + c.monthList[d] + "</span>" + c.month), x.val(a), y.val(d + 1), this }, f.selected = function (a) { var c, d; return c = y.val(), d = a, "yyyy-mm-dd" == b.type && (c = "0" + y.val(), d = "0" + a, c = c.substr(c.length - 2, c.length), d = d.substr(d.length - 2, d.length)), e.val(x.val() + b.hyphen + c + b.hyphen + d), e.trigger("change"), f.hide(), this }, f.clear = function () { return e.val(""), f.change(j, k), f.hide(), this }, f.getdate = function () { return e.val() }, f.setdate = function (b, c, d) { if ("string" == typeof b) return f.setdate({ date: b }), void 0; if ("number" == typeof b && "number" == typeof c && "number" == typeof d) return f.setdate({ year: b, month: c, day: d }), void 0; if ("object" != typeof b) return !1; b = a.extend({}, { date: null, year: null, month: null, day: null, set: !0 }, b); var e; return "string" == typeof b.date && (b.date = h(b.date), e = new Date(b.date), e.setHours(0), e.setMinutes(0), e.setSeconds(0), b.year = e.getFullYear(), b.month = e.getMonth() + 1, b.day = e.getDate()), f.change(b.year, b.month), b.set && f.selected(b.day), this }, f.gotodate = function (a, b) { return "string" == typeof a ? f.setdate({ date: a, set: !1 }) : "number" == typeof a && "number" == typeof b && f.setdate({ year: a, month: b, day: 1, set: !1 }), this }, q.delegate("a", "click", function () { if (this.rel) { var a = this.rel; switch (a) { case "pre": return f.change(x.val(), parseInt(y.val(), 10) - 1), !1; case "next": return f.change(x.val(), parseInt(y.val(), 10) + 1), !1; case "clear": return f.clear(), !1 } } }), t.delegate("td", "click", function () { var b = a(this); b.hasClass("num") && (t.find("td").removeClass("selected"), b.addClass("selected"), f.selected(b.data("day"))) }), e.bind("click", f.show), u.bind("click", f.hide), w.bind("click", function () { w.hide(), s.show() }), x.bind("change", function () { f.change(x.val(), y.val()) }), y.bind("change", function () { f.change(x.val(), y.val()) }), f.change(j, k), g = { jqobj: d.jqobj, show: f.show, hide: f.hide, getdate: f.getdate, setdate: f.setdate, gotodate: f.gotodate, clear: f.clear} } }, a.cxCalendar = { defaults: { beginyear: 1950, endyear: 2030, date: new Date, type: "yyyy-mm-dd", hyphen: "-", wday: 0 }, language: { year: "年", month: "月", monthList: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], weekList: ["日", "一", "二", "三", "四", "五", "六"]}} } (jQuery);