<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var bm=lang["DlgPagi"];document.write("<title>"+bm+"</title>");var wy=EWIN.document.getElementById("D_PaginationTitle");function aq(){lang.ag(document);$("d_autonum").value=config.PaginationAutoNum;var ir=1;var K=EWEB.T.getElementsByTagName("IMG");for(var i=0;i<K.length;i++){var el=K[i];var cb=el.getAttribute("_ewebeditor_fake_tag",2);if(cb){if(cb.toLowerCase()=="pagination"){ir++;}}}var iv=wy.value.split("\r\n");for(var i=0;i<ir;i++){var fg="";if(iv.length>i){fg=iv[i];}$("d_title_select").options[$("d_title_select").options.length]=new Option((i+1)+") "+fg,fg);}$("d_title_select").selectedIndex=0;$("d_title_input").value=$("d_title_select").options[0].value;parent.ar(bm);};function zF(){$("d_title_input").value=$("d_title_select").options[$("d_title_select").selectedIndex].value;};function Ax(){var i=$("d_title_select").selectedIndex;if(i==0){return;}var v1=$("d_title_select").options[i-1].value;var v2=$("d_title_select").options[i].value;$("d_title_select").options[i-1]=new Option(i+") "+v2,v2);$("d_title_select").options[i]=new Option((i+1)+") "+v1,v1);$("d_title_select").selectedIndex=i-1;uo();};function AK(){var i=$("d_title_select").selectedIndex;var l=$("d_title_select").options.length;if(i==l-1){return;}var v1=$("d_title_select").options[i].value;var v2=$("d_title_select").options[i+1].value;$("d_title_select").options[i]=new Option((i+1)+") "+v2,v2);$("d_title_select").options[i+1]=new Option((i+2)+") "+v1,v1);$("d_title_select").selectedIndex=i+1;uo();};function AA(){var v=$("d_title_input").value;var i=$("d_title_select").selectedIndex;$("d_title_select").options[i]=new Option((i+1)+") "+v,v);$("d_title_select").selectedIndex=i;uo();};function uo(){var v="";for(var i=0;i<$("d_title_select").options.length;i++){v+=$("d_title_select").options[i].value+"\r\n";}wy.value=v;};function xE(){EWIN.dp.fv();parent.bV();};function yj(){EWIN.dp.Empty();parent.bV();};function Aj(){var v=$("d_autonum").value;if(!v){bX($("d_autonum"),lang["ErrInput"]);return;}EWIN.dp.Auto(v);parent.bV();} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td> <fieldset> <legend><span lang=DlgPagiTitle></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap><select size=8 id="d_title_select" style="width:300px" onchange="zF()"></select></td> <td noWrap> <table border=0 cellpadding=0 cellspacing=0> <tr><td><input type=button lang="DlgPagiTitleUp" onclick="Ax()" style="width:60px" class="dlgBtn"></td></tr> <tr><td height=2></td></tr> <tr><td><input type=button lang="DlgPagiTitleDown" onclick="AK()" style="width:60px" class="dlgBtn"></td></tr> </table> </td> </tr> <tr> <td><input type=text id=d_title_input style="width:300px"></td> <td><input type=button lang="DlgPagiTitleModi" onclick="AA()" style="width:60px" class="dlgBtn"></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <fieldset> <legend><span lang=DlgPagiAuto></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td align=right><span lang=DlgPagiAutoNum></span>: <input style="width:80px" type=text id=d_autonum size=10 value="" onkeydown="eR(event);"> <input type=button lang=DlgPagiAutoBtn onclick="Aj()" class="dlgBtn"></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <table width="100%" border=0 cellpadding=0 cellspacing=0> <tr> <td noWrap><input type=button lang=DlgPagiKeyInsert onclick="xE()" class="dlgBtn"> <input type=button lang=DlgPagiKeyEmpty onclick="yj()" class="dlgBtn"></td> <td noWrap align=right><input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnClose></td> </tr> </table> </td> </tr> </table> </td></tr></table> </body> </html>