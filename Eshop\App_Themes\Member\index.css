﻿body{margin:0;}
table{border-collapse:collapse;empty-cells:show;table-layout:fixed;}
*{font-size:12px;}
body{margin:0px;}
BODY,td,div,table,ul,p,form{font-size:9pt;word-break:break-all;margin:0px;padding:0px;}
td{font-family:宋体;font-size:9pt;}
li{list-style-type:none;}
.clear,.cl{clear:both;height:0px;font:0px/0px Arial;}
/*超链接样式*/
a{border:0;background-color:transparent;cursor:pointer;}
a:link{font-size:12px;text-decoration:none;color:#06527d;cursor:pointer;}
a:visited{font-size:12px;text-decoration:none;color:#06527d;cursor:pointer;}
a:hover{font-size:12px;text-decoration:underline;color:#FF0000;cursor:pointer;}
a img{border:0px;}
a.v1:link{font-size:12px;text-decoration:none;color:#000000;}
a.v1:visited{font-size:12px;text-decoration:none;color:#000000;}
a.v1:hover{font-size:12px;text-decoration:underline;color:#FF0000;}
table tr td img{cursor:pointer;}
/*tabT 头部信息样式*/
.tabT{height:30px;background:url(../../images/Member/tab_05.gif) repeat-x top;display:block;margin-top:2px;}
.tabTR{background:url(../../images/Member/tab_03.gif) no-repeat left top;	display:block;height:30px;float:left;width:10px;}
.tabTL{background:url(../../images/Member/tab_07.gif) no-repeat right top;display:block;height:30px;float:right;width:10px;}
.tablText{float:left;line-height:20px;height:30px;}
.tablText img{margin-top:8px;}
.tablText .tab4{font-size:12px;color:#1F4A65;font-weight:bold;}
.tabTAll{line-height:30px;position:absolute;height:30px;width:auto;right:20px;text-align:right;}
.tabTAll a{font-size:12px;text-decoration:none;color:#000;padding-left:8px;}
.STms{padding-left:30px;background:url(../../images/Member/main_51.gif) no-repeat 12px center;color:#2058FF;cursor:pointer;}
/*tabC 中部信息样式*/
.tabC{display:block;margin:0px;background-color:#DFE8F6;}
.tabCR{background:url(../../images/Member/tab_12.gif) repeat-y left;display:block;}
.tabCL{background:url(../../images/Member/tab_16.gif) repeat-y right;display:block;padding:0px 12px;}
.tabSP{table-layout:fixed;}
.tabSP,.tabSP2,.tabSP3{background-color:#FFF;border:1px solid #DFE8F6;border-left:none;border-bottom:none;}
.tabSP .div{background-color:#FFF;border:1px solid #DFE8F6;border-top:none;}
.tabSP tr td span{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;display:block;height:20px;width:100%;}
.tabSP tr td{background-color:#FFF;height:23px;line-height:23px;padding:2px 3px;border-left:1px solid #DFE8F6;border-bottom:1px solid #DFE8F6;}
.tabSP .tabHe td,.tabSP2 .tabHe td{background:url(../../images/Member/tab_14.gif) repeat-x top;text-align:center;height:26px;line-height:26px;padding:0px;}
.tabSP td a.icp {padding:3px 0xp 0px;display:block;}
.tabSP td a.icp img {border: 1px solid #E0E0E0;}
.tabSP tr td span {display:block;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:100%;height:25px;line-height:25px;}
.tabSP2 tr td{background-color:#FFF;height:auto;line-height:20px;padding:5px;border-left:1px solid #DFE8F6;border-bottom:1px solid #DFE8F6;}
.tabSP2 tr td a{text-overflow:ellipsis;white-space:nowrap;}
/*tabB 底部信息样式*/
.tabB{background:url(../../images/Member/tab_21.gif) repeat-x top;display:block;height:29px;width:auto;position:relative;}
.tabBR{background:url(../../images/Member/tab_20.gif) no-repeat left top;float:left;height:29px;width:10px;}
.tabBL{background:url(../../images/Member/tab_22.gif) no-repeat right top;float:right;height:29px;width:10px;}
.tabBC{line-height:25px;position:absolute;height:25px;width:96%;text-align:center;margin:0px auto;bottom:3px;}
.tabBC img{cursor:pointer;}
.tabBCL {height:25px;line-height:25px;margin:0px auto;float:left;padding-left:40px;}
.tabBCC {height:25px;line-height:25px;margin:0px auto;float:left;padding-left:40px;}
.tabBCR {height:25px;line-height:25px;margin:0px auto;float:right;display:block;}
.tabBCR a,.tabBCR span {float:left;display:block;padding:0px 3px;}
.tabBCR img,.tabBCR input {margin-top:4px;text-align:center;}
.TextBox {height:12px; width:20px; border:1px solid #999999;}
.tabBC .tabBCL span,.tabBC .tabBCL a,.tabBC .tabBCC span,.tabBC .tabBCC a,.tabBC .tabBCC b {border:1px solid #DFE8F6; padding:2px 3px; margin:0px 2px;}
.tabBC .tabBCC span {font-weight:bold;}
/*********信息显示层*********/
.shieldH {
background-color: #000;
width: 100%;height: 100%;left:0;top:0;filter:alpha(opacity=50);opacity:0.2;z-index:50000;
position:fixed!important;position:absolute;
_top:expression_r(eval_r(document.compatMode &&
	document.compatMode=='CSS1Compat') ?
	documentElement.scrollTop + (document.documentElement.clientHeight-this.offsetHeight)/2 :
	document.body.scrollTop + (document.body.clientHeight - this.clientHeight)/2);
}
.SXH {
padding:10px;position:absolute;z-index:50002;background:#FFFFFF;
font-size: 12px;font-weight: bold;
width: 320px;height: auto;
left:50%;top: 50%;
margin-left:-150px!important;margin-top:-60px!important;margin-top:0px;
position:fixed!important;position:absolute;
_top:expression_r(eval_r(document.compatMode &&
	document.compatMode=='CSS1Compat') ?
	documentElement.scrollTop + (document.documentElement.clientHeight-this.offsetHeight)/2 :
	document.body.scrollTop + (document.body.clientHeight - this.clientHeight)/2);
}
#tooltip,.tooltip{position:absolute;border:1px solid #ccc;background:#FFFFFF;padding:2px;display:none;color:#fff;z-index:1000;Zoom:1;display:block;float:left;}
.inpqx{text-align:center;}
.inpqx a,.inpqx2 a{border:1px solid #DFE8F6;color:#06527d;padding:2px 5px; margin:0px 6px;text-decoration: none;cursor:pointer;}
.inpqx a:hover,.inpqx2 a:hover,a:hover.Lcl{border: 1px solid #1F4A65;}
.img img,.imgs img{background-repeat:no-repeat;background-position: center center;width:25px;height:20px;}
.imgs img{width:60px !important;}
.gra{color:#A3A3A3;}
.tabSP .html td{line-height:25px;padding:3px;}
.tabSP .html td label{float:left;display:block;padding-left:8px;text-overflow:ellipsis;white-space:nowrap;height:25px;}
.tabSP .html td label i{font-style:normal;}
.iocy,.Posi{display:block;text-align:center;}
.iocy em,.Posi em{display:block;width:18px;height:18px;background:url(../../image/sify.png) no-repeat -1000px -1000px;overflow:hidden;margin:0px auto;}
.Posi em{background:url(../../image/Posi.png) no-repeat -1000px -1000px;}
em.sif-1{background-position: 0px 0px !important;}
em.sif-2{background-position: -32px 0px !important;}
em.sif-3{background-position: -66px 0px !important;}
em.sif-4{background-position: -99px 0px !important;}
em.sif-5{background-position: -133px 0px !important;}
em.sif-6{background-position: -166px 0px !important;}
em.sif-7{background-position: 0px -27px !important;}
em.sif-8{background-position: -32px -27px !important;}
em.sif-9{background-position: -66px -27px !important;}
em.sif-10{background-position: -99px -27px !important;}
em.sif-11{background-position: -133px -27px !important;}
em.sif-12{background-position: -166px -27px !important;}
em.sif-13{background-position: 0px -53px !important;}
em.sif-14{background-position: -32px -53px !important;}
em.sif-15{background-position: -66px -53px !important;}
em.sif-16{background-position: -99px -53px !important;}
em.sif-16{background-position: -133px -53px !important;}
em.sif-17{background-position: -166px -53px !important;}
em.sif-18{background-position: 0px -79px !important;}
em.sif-19{background-position: -32px -79px !important;}
em.sif-20{background-position: -66px -79px !important;}
em.sif-21{background-position: -99px -79px !important;}
em.sif-22{background-position: -133px -79px !important;}
em.sif-23{background-position: -166px -79px !important;}