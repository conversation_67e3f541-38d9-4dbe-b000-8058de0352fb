<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var bm=lang["DlgMap"];document.write("<title>"+bm+"</title>");var t;var wI;var wO;var gs=null;var vP="";var nR="";var bO="";var bD="";var ra=new Array();var bY=new Array();var rQ="";var wm;var dJ;t=C.ax();vP=t.src;nR=t.useMap;bO=t.offsetWidth;bD=t.offsetHeight;var vx=EWEB.T.body.getElementsByTagName("MAP");for(var i=0;i<vx.length;i++){ra[i]=vx[i].name.toUpperCase();if(("#"+ra[i])==nR.toUpperCase()){gs=vx[i];}}rQ="<img id='SOURCEIMAGE' border=0 src='"+vP+"' width='"+bO+"' height='"+bD+"'>";if(gs){for(var i=0;i<gs.areas.length;i++){bY[i]=new Object();bY[i].Href=vH(gs.areas[i],"href");bY[i].Target=gs.areas[i].target;bY[i].Coords=gs.areas[i].coords;var a=bY[i].Coords.split(",");bY[i].Left=parseInt(a[0]);bY[i].Top=parseInt(a[1]);bY[i].Width=parseInt(a[2])-bY[i].Left;bY[i].Height=parseInt(a[3])-bY[i].Top;rQ+="<img id='myIMAGE"+i+"' border=1 src='../sysimage/space.gif' style='position:absolute;left:"+bY[i].Left+"px;top:"+bY[i].Top+"px;width:"+bY[i].Width+"px;height:"+bY[i].Height+"px;zIndex:"+(i+1)+"'>";}}function xA(){wm=$("mapIframe").contentWindow;dJ=wm.document;dJ.designMode="On";dJ.open();dJ.write("<head><style>body,a,table,td {font-size:9pt;font-family:Verdana, Arial, Helvetica, sans-serif;Color:#000000;}</style></head><body MONOSPACE>");dJ.body.innerHTML=rQ;dJ.body.contentEditable="true";dJ.execCommand("2D-Position",true,true);dJ.execCommand("LiveResize",true,true);dJ.close();R.az(dJ,'paste',R.aw);R.az(dJ,'help',R.aw);R.az(dJ,'keydown',Cn);R.az(dJ,'contextmenu',R.aw);R.az(dJ,'dblclick',AF);};var gv=new Object();function AF(e){if(!e){e=mapIframe.event;}var dZ=e.target||e.srcElement;if(dZ){if((dZ.tagName=="IMG")&&(dZ.id!="SOURCEIMAGE")){var zQ=dZ.id;var qZ=parseInt(zQ.substr(7));gv.Href=bY[qZ].Href;gv.Target=bY[qZ].Target;gv.Index=qZ;ec.OpenDialog("hyperlink.htm?action=other&returnfieldflag="+qZ);}}return R.aw(e);};function Cn(e){if(!e){e=mapIframe.event;}var bK=e.keyCode||e.which;if(bK==46){var cT,fH,oc;if(F.as){fH=dJ.selection;cT=fH.type;if(cT="Control"){oc=fH.createRange().item(0);}}else{var At={img:1,hr:1,li:1,table:1,tr:1,td:1,embed:1,object:1,ol:1,ul:1};fH=wm.getSelection();if(fH&&fH.rangeCount==1){var fA=fH.getRangeAt(0);if(fA.startContainer==fA.endContainer&&(fA.endOffset-fA.startOffset)==1&&fA.startContainer.nodeType==1&&At[fA.startContainer.childNodes[fA.startOffset].nodeName.toLowerCase()]){cT='Control';oc=fA.startContainer.childNodes[range.startOffset];}}}if(oc){if((oc.tagName=="IMG")&&(oc.id!="SOURCEIMAGE")){return true;}}}return R.aw(e);};function zR(){bY[gv.Index].Href=gv.Href;bY[gv.Index].Target=gv.Target;};function AL(){var n=bY.length;bY[n]=new Object();bY[n].Href="";bY[n].Target="";dJ.body.innerHTML+="<img id='myIMAGE"+n+"' border=1 src='../sysimage/space.gif' style='position:absolute;zIndex:"+(n+1)+";width:20;height:20;left:0;top:0'>";};function zk(){var b=true;var n=0;var s="";while(b){n++;s="AutoMap"+n;if(zS(s)){b=false;}}return s;};function zS(bM){bM=bM.toUpperCase();for(var i=0;i<ra.length;i++){if(ra[i]==bM){return false;}}return true;};function ok(){var b=false;for(var i=0;i<bY.length;i++){var obj=dJ.getElementById("myIMAGE"+i);if(obj){b=true;}}if(b){var html="";for(var i=0;i<bY.length;i++){var Ci=bY[i];var obj=dJ.getElementById("myIMAGE"+i);if(obj){var wC=parseInt(obj.style.left);var vW=parseInt(obj.style.top);var zc=parseInt(obj.style.width);var Ac=parseInt(obj.style.height);var zi=wC+zc;var Am=vW+Ac;html+="<area shape='rect' href='"+Ci.Href+"' target='"+Ci.Target+"' coords='"+wC+","+vW+","+zi+","+Am+"'>";}}if(gs){gs.innerHTML=html;}else{nR=zk();t.useMap="#"+nR;EWIN.appendHTML("<map name='"+nR+"'>"+html+"</map>");}}else{if(gs){R.cE(gs);}t.useMap="";}t.width=dJ.getElementById("SOURCEIMAGE").width;t.height=dJ.getElementById("SOURCEIMAGE").height;parent.bV();};function aq(){lang.ag(document);xA();parent.ar(bm);} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=3 align=center width="600" height="400"> <tr><td colspan=2 height="100%"><iframe ID="mapIframe" MARGINHEIGHT="1" MARGINWIDTH="1" width="100%" scrolling="yes" height="100%" src="blank.htm"></iframe></td></tr> <tr><td colspan=2 height=5></td></tr> <tr> <td><input type=button class="dlgBtnCommon dlgBtn" value='' id=btnNew onclick="AL()" lang=DlgMapNew>&nbsp;(<span lang=DlgMapDesc></span>)</td> <td align=right><input type=button class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel></td> </tr> </table> </td></tr></table> </body> </html> 