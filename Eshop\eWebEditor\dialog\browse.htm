<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var gX=cc["type"];var wo=cc["returnfieldflag"];var aa="";var oe="";var uk=new Object();var ql="";var il="nameasc";var wM="thumbnail";var fI="";var ip="";var so="";var hD=1;var Cy="20";switch(gX){case "image":aa=lang["DlgBrowseImage"];oe=lang["DlgBrowseUploadImage"];break;case "flash":aa=lang["DlgBrowseFlash"];oe=lang["DlgBrowseUploadFlash"];break;case "media":aa=lang["DlgBrowseMedia"];oe=lang["DlgBrowseUploadMedia"];break;default:gX="file";aa=lang["DlgBrowseFile"];oe=lang["DlgBrowseUploadFile"];break;}var bm=lang["DlgBrowse"]+"("+aa+")";document.write("<title>"+bm+"</title>");var lu=null;function tx(dn,fY,hA,gi){if(lu==gi){return;}hD=1;var jL;if(lu){jL=lu.childNodes[0];jL.src=jL.src.replace(/diropen/g,"dirclose");lu.childNodes[1].className="nodetext";}lu=gi;jL=lu.childNodes[0];jL.src=jL.src.replace(/dirclose/g,"diropen");lu.childNodes[1].className="nodetextselected";if(uk[hA]){ql=hA;wt(dn,fY);lv();}else{$("myIframe").contentWindow.location.replace("../"+config.ServerExt+"/browse."+config.ServerExt+"?action=file&type="+gX+"&dir="+Aa(fY)+"&style="+aA.StyleName+"&cusdir="+aA.CusDir+"&skey="+aA.SKey+"&foldertype="+dn+"&returnflag="+hA);}};function Aa(s){if((config.ServerExt!="php")&&(config.ServerExt!="jsp")){return s;}s=encodeURIComponent(s);return s.replace(/~/g,'%7E').replace(/%20/g,'+');};function xg(){$("myIframe").contentWindow.location.replace("../"+config.ServerExt+"/browse."+config.ServerExt+"?action=folder&type="+gX+"&style="+aA.StyleName+"&cusdir="+aA.CusDir+"&skey="+aA.SKey);};function setFolderList(AU,xo,yI,xU,AD){var html;html="<div class=node><img border=0 src='images/tree/root.gif' align=absmiddle><span class=nodetext>"+aa+"</span></div>";html+=ig(AU,false,"upload",oe);switch(gX){case "image":html+=ig(xo,true,"shareimage",lang["DlgBrowseShareImage"]);break;case "flash":html+=ig(yI,true,"shareflash",lang["DlgBrowseShareFlash"]);break;case "media":html+=ig(xU,true,"sharemedia",lang["DlgBrowseShareMedia"]);break;default:html+=ig(xo,false,"shareimage",lang["DlgBrowseShareImage"]);html+=ig(yI,false,"shareflash",lang["DlgBrowseShareFlash"]);html+=ig(xU,false,"sharemedia",lang["DlgBrowseShareMedia"]);html+=ig(AD,true,"shareother",lang["DlgBrowseShareFile"]);break;}$("divFolder").innerHTML=html;};function ig(by,tC,dn,fg){var py=new Array();var rO=new Array();var html="";var qP,sY,gE,wP,rm,mn,fY,iC;if(tC){iC="blank.gif";}else{iC="line.gif";}py[0]="<img border=0 src='images/tree/"+iC+"' align=absmiddle>";rO[0]="";qP=by.length;if(qP>0){if(tC){iC="plus2.gif";}else{iC="plus1.gif";}html+="<div class=node><img border=0 src='images/tree/"+iC+"' align=absmiddle onclick=\"qI('nodediv_"+dn+"',this)\"><span onclick=\"tx('"+dn+"','','span_"+dn+"',this)\"><img border=0 src='images/tree/dirclose.gif' align=absmiddle><span class=nodetext>"+fg+"</span></span></div>";html+="<div class=nodediv id='nodediv_"+dn+"' style='display:none'>";for(var i=0;i<qP;i++){sY=by[i][0];gE=by[i][1];wP=by[i][2];rm=false;if((i+1)<qP){if(by[i+1][1]>gE){rm=true;}}if(i>0){if(gE>by[i-1][1]){html+="<div class=nodediv id='nodediv_"+dn+"_"+(i-1)+"' style='display:none'>";}mn=by[i-1][1]-gE;if(mn>0){for(var j=0;j<mn;j++){html+="</div>";}}}html+="<div class=node>";for(var j=0;j<gE;j++){html+=py[j];}if(wP==0){py[gE]="<img border=0 src='images/tree/line.gif' align=absmiddle>";if(rm){html+="<img border=0 src='images/tree/plus1.gif' align=absmiddle onclick=\"qI('nodediv_"+dn+"_"+i+"',this)\">";}else{html+="<img border=0 src='images/tree/branch1.gif' align=absmiddle>";}}else{py[gE]="<img border=0 src='images/tree/blank.gif' align=absmiddle>";if(rm){html+="<img border=0 src='images/tree/plus2.gif' align=absmiddle onclick=\"qI('nodediv_"+dn+"_"+i+"',this)\">";}else{html+="<img border=0 src='images/tree/branch2.gif' align=absmiddle>";}}rO[gE]=sY+"/";fY="";for(var j=0;j<gE;j++){fY+=rO[j+1];}html+="<span class=nodetext onclick=\"tx('"+dn+"','"+fY+"','span_"+dn+"_"+i+"',this)\"><img border=0 src='images/tree/dirclose.gif' align=absmiddle><span class=nodetext>"+sY+"</span></span>";html+="</div>";}mn=gE-1;if(mn>0){for(var j=0;j<mn;j++){html+="</div>";}}html+="</div>";}else{if(tC){iC="branch2.gif";}else{iC="branch1.gif";}html+="<div class=node><img border=0 src='images/tree/"+iC+"' align=absmiddle onclick=\"qI('nodediv_"+dn+"',this)\"><span onclick=\"tx('"+dn+"','','span_"+dn+"',this)\"><img border=0 src='images/tree/dirclose.gif' align=absmiddle><span class=nodetext>"+fg+"</span></span></div>";html+="<div class=nodediv id='nodediv_"+dn+"' style='display:none'>";html+="</div>";}return html;};function setFileList(hA,dn,fY,AP){uk[hA]=AP;ql=hA;wt(dn,fY);lv();};function lv(){ip="";fI="";if(ql==""){return;}var by;if(F.Cf){var Et=uk[ql];by=new Array();for(var i=0;i<Et.length;i++){by[i]=new Array(Et[i][0],Et[i][1],Et[i][2]);}}else{by=uk[ql];}var l=by.length;if(l==0){$("divFile").innerHTML="";return;}switch(il){case "nameasc":by.sort(function(x,y){return x[0].localeCompare(y[0])});break;case "namedesc":by.sort(function(x,y){return y[0].localeCompare(x[0])});break;case "sizeasc":by.sort(function(x,y){return parseFloat(x[1])-parseFloat(y[1])});break;case "sizedesc":by.sort(function(x,y){return parseFloat(y[1])-parseFloat(x[1])});break;case "typeasc":by.sort(function(x,y){var ab=x[0].substr(x[0].lastIndexOf(".")+1);ab=ab+"          ";ab=ab.substr(0,10);ab=ab+x[0];var aF=y[0].substr(y[0].lastIndexOf(".")+1);aF=aF+"          ";aF=aF.substr(0,10);aF=aF+y[0];return ab.localeCompare(aF)});break;case "typedesc":by.sort(function(x,y){var ab=y[0].substr(y[0].lastIndexOf(".")+1);ab=ab+"          ";ab=ab.substr(0,10);ab=ab+y[0];var aF=x[0].substr(x[0].lastIndexOf(".")+1);aF=aF+"          ";aF=aF.substr(0,10);aF=aF+x[0];return ab.localeCompare(aF)});break;case "timeasc":by.sort(function(x,y){return x[2].localeCompare(y[2])});break;case "timedesc":by.sort(function(x,y){return y[2].localeCompare(x[2])});break;}var jD=1;var yc=$("d_perpagenum").options[$("d_perpagenum").selectedIndex].value;var ly;var lq=0;var eQ=l;if(yc!="all"){ly=parseInt(yc);if(l%ly==0){jD=l/ly;}else{jD=parseInt(l/ly+1);}if(hD>jD){hD=1;}if(hD>1){lq=(hD-1)*ly;}if(hD<jD){eQ=hD*ly;}}var html="";var m,n;switch(wM){case "detail":var jg,pv;if(il){jg=il.substr(0,4);pv=il.substr(4);}else{jg="name";pv="asc";}var jf="";var yo="";var xI="";var yk="";var yp="";if(pv=="asc"){jf="images/tree/arrowup.gif";}else{jf="images/tree/arrowdown.gif";}jf="&nbsp;<img border=0 align=absmiddle src='"+jf+"'>";if(jg=="name"){yo=jf}if(jg=="size"){xI=jf}if(jg=="type"){yk=jf}if(jg=="time"){yp=jf}html="<table border=0 cellpadding=1 cellspacing=1 width='100%'>";html+="<tr style='BACKGROUND-COLOR: #f0f0f0'>"+"<td width='20' align=center>&nbsp;</td>"+"<td width='185' align=left onclick=\"rn('name')\">"+lang["DlgBrowseSortName"]+yo+"</td>"+"<td width='75' align=left onclick=\"rn('type')\">"+lang["DlgBrowseSortType"]+yk+"</td>"+"<td width='130' align=center onclick=\"rn('time')\">"+lang["DlgBrowseSortTime"]+yp+"</td>"+"<td width='85' align=right onclick=\"rn('size')\">"+lang["DlgBrowseSortSize"]+xI+"</td>"+"</tr>";for(var i=lq;i<eQ;i++){html+="<tr align=center id='item_tr_"+i+"' onclick=\"zB('"+i+"')\" onmouseover=\"xT('"+i+"')\" onmouseout=\"xP('"+i+"')\">"+"<td><img border=0 src='../sysimage/icon16/"+iM(by[i][0])+"' align=absmiddle></td>"+"<td align=left id='item_filename_"+i+"'>"+by[i][0]+"</td>"+"<td align=left>"+by[i][0].substr(by[i][0].lastIndexOf(".")+1)+"</td>"+"<td align=center>"+by[i][2]+"</td>"+"<td align=right>"+by[i][1]+"</td>"+"</tr>";}html+="</table>";break;case "thumbnail":html="<table border=0 cellpadding=0 cellspacing=0 width='100%'>";for(var i=lq;i<eQ;i++){m=(i+1)%4;if(m==1){html+="<tr>";}html+="<td align=center valign=top width='25%'>"+"<table border=0 cellpadding=0 cellspacing=0 onclick=\"zm('"+i+"')\" style='table-layout:fixed;word-wrap:break-word;'><tr><td>"+"<table border=0 cellpadding=1 cellspacing=3 id='item_table_"+i+"'><tr><td bgcolor=#ffffff>"+"<table border=0 cellspacing=1 cellpadding=0 width=120 height=120 style='border:1px solid #808080; table-layout:fixed;word-wrap:break-word;'><tr><td align=center valign=middle>"+Ah(by[i][0])+"</td></tr></table>"+"</td></tr></table>"+"</td></tr>"+"<tr><td align=center><span id='item_span_"+i+"'>"+by[i][0]+"</span></td></tr>"+"</table>"+"</td>";if(m==0){html+="</tr>";}}if(m!=0){for(var i=0;i<(4-m);i++){html+="<td width='25%'></td>";}html+="</tr>";}html+="</table>";break;case "icon":html="<table border=0 cellpadding=0 cellspacing=5 width='100%'>";for(var i=lq;i<eQ;i++){m=(i+1)%5;if(m==1){html+="<tr>";}html+="<td valign=top width='20%'>"+"<table border=0 cellpadding=0 cellspacing=1 onclick=\"zK('"+i+"')\" style='table-layout:fixed;'>"+"<tr><td align=center><img id='item_img_"+i+"' border=0 align=absmiddle src='../sysimage/icon32/"+iM(by[i][0])+"'></td></tr>"+"<tr><td align=center height=30 valign=top id='item_td_"+i+"' style='word-wrap:break-word;line-height:1'>"+by[i][0]+"</td></tr>"+"</table>"+"</td>";if(m==0){html+="</tr>";}}if(m!=0){for(var i=0;i<(5-m);i++){html+="<td width='20%'></td>";}html+="</tr>";}html+="</table>";break;case "list":html="<table border=0 cellpadding=0 cellspacing=3 width='100%'>";for(var i=lq;i<eQ;i++){m=(i-lq+1)%3;if(m==1){html+="<tr>";}html+="<td valign=top width='33%' onclick=\"Ak('"+i+"')\">"+"<table border=0 cellpadding=0 cellspacing=0 id='item_table_"+i+"'><tr><td><img border=0 align=absmiddle src='../sysimage/icon16/"+iM(by[i][0])+"'></td><td width=2></td><td id='item_td_"+i+"'>"+by[i][0]+"</td></tr></table>"+"</td>";if(m==0){html+="</tr>";}}if(m!=0){for(var i=0;i<(3-m);i++){html+="<td width='33%'></td>";}html+="</tr>";}html+="</table>";break;}$("divFile").innerHTML=html;$("d_pagenav").innerHTML=AQ(jD);};function nJ(n){hD=parseInt(n);lv();};function yP(){qz("BrowsePerPageNum",$("d_perpagenum").options[$("d_perpagenum").selectedIndex].value);hD=1;lv();};function AQ(jD){var mC=hD;if($("d_perpagenum").options[$("d_perpagenum").selectedIndex].value=="all"){return "";}var html="<table border=0 cellpadding=0 cellspacing=0><tr><td>";if(mC>1){html+="<span style='cursor:hand;color:#0000ff;text-decoration:underline' onclick='nJ(1)' title='"+lang["DlgBrowsePageFirst"]+"'>"+lang["DlgBrowsePageFirst"]+"</span> ";html+="<span style='cursor:hand;color:#0000ff;text-decoration:underline' onclick='nJ("+(mC-1)+")' title='"+lang["DlgBrowsePagePre"]+"'>"+lang["DlgBrowsePagePre"]+"</span> ";}else{html+="<span style='' title='"+lang["DlgBrowsePageFirst"]+"'>"+lang["DlgBrowsePageFirst"]+"</span> ";html+="<span style='' title='"+lang["DlgBrowsePagePre"]+"'>"+lang["DlgBrowsePagePre"]+"</span> ";}if(mC<jD){html+="<span style='cursor:hand;color:#0000ff;text-decoration:underline' onclick='nJ("+(mC+1)+")' title='"+lang["DlgBrowsePageNext"]+"'>"+lang["DlgBrowsePageNext"]+"</span> ";html+="<span style='cursor:hand;color:#0000ff;text-decoration:underline' onclick='nJ("+jD+")' title='"+lang["DlgBrowsePageLast"]+"'>"+lang["DlgBrowsePageLast"]+"</span> ";}else{html+="<span style='' title='"+lang["DlgBrowsePageNext"]+"'>"+lang["DlgBrowsePageNext"]+"</span> ";html+="<span style='' title='"+lang["DlgBrowsePageLast"]+"'>"+lang["DlgBrowsePageLast"]+"</span> ";}html+="</td><td>";html+="&nbsp;<select size=1 id=d_pageoption onchange='nJ(this.options[this.selectedIndex].value)'>";for(var i=1;i<=jD;i++){if(i==mC){html+="<option value='"+i+"' selected>"+i+"</option>";}else{html+="<option value='"+i+"'>"+i+"</option>";}}html+="</select>";html+="</td></tr></table>";return html;};function wt(dn,fY){var hj="";dn=dn.toLowerCase();switch(dn){case "upload":hj=config.UploadUrl;var ki=config.CusDir||aA.CusDir;if(ki){hj+=ki;if(hj.substr(hj.length-1)!="/"){hj+="/";}}break;case "shareimage":hj="sharefile/image/";break;case "shareflash":hj="sharefile/flash/";break;case "sharemedia":hj="sharefile/media/";break;case "shareother":hj="sharefile/other/";break;}so=hj+fY;$("d_folderpath").innerHTML="/"+fY;};function zB(dc){var jz,xB;if(fI!=""){jz=document.getElementById("item_tr_"+fI);jz.className="detailout";}jz=document.getElementById("item_tr_"+dc);jz.className="detailselected";xB=document.getElementById("item_filename_"+dc);ip=xB.innerHTML;fI=dc;};function xT(dc){if(fI==dc){return;}else{jz=document.getElementById("item_tr_"+dc);jz.className="detailover";}};function xP(dc){if(fI==dc){return;}else{jz=$("item_tr_"+dc);jz.className="detailout";}};function zK(dc){var im,qp;if(fI!=""){im=$("item_td_"+fI);im.className="iconitem";qp=$("item_img_"+fI);qp.style.filter="";}im=$("item_td_"+dc);im.className="iconitemselected";qp=$("item_img_"+dc);qp.style.filter="gray";ip=im.innerText;fI=dc;};function Ak(dc){var jA,im;if(fI!=""){jA=$("item_table_"+fI);jA.className="listitem";}jA=$("item_table_"+dc);jA.className="listitemselected";im=$("item_td_"+dc);ip=im.innerText;fI=dc;};function zm(dc){var jA,qM;if(fI!=""){jA=$("item_table_"+fI);jA.className="thumbnailitem";qM=$("item_span_"+fI);qM.className="thumbnailitem";}jA=$("item_table_"+dc);jA.className="thumbnailitemselected";qM=$("item_span_"+dc);qM.className="thumbnailitemselected";ip=qM.innerHTML;fI=dc;};function Ah(qi){var kd=so;if(config.BaseUrl!="3"){if(kd.substr(0,1)!="/"){kd="../"+kd;}}else{if(kd.substring(0,5)=="share"){kd="../"+kd;}}var AC="../sysimage/icon16/"+iM(qi);var cs=qi.substr(qi.lastIndexOf(".")+1).toLowerCase();var html="";var wx=kd+qi;if((cs=="gif")||(cs=="jpg")||(cs=="jpeg")||(cs=="bmp")||(cs=="png")){html="<img border=0 src='"+wx+"' onload='Aw(this)'>";}else if(cs=="swf"){html=wF(wx,115,115);}else{html="<img border=0 src='"+AC+"' style='position:relative;left:50px;top:50px'>";}return html};function wF(url,w,h){var bM='<object width="'+w+'" height="'+h+'" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" codebase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,0,0">'+'<param name="movie" value="'+url+'">'+'<param name="wmode" value="opaque">'+'<param name="quality" value="autohigh">'+'<embed width="'+w+'" height="'+h+'" src="'+url+'" quality="autohigh" wmode="opaque" type="application/x-shockwave-flash" plugspace="http://www.macromedia.com/shockwave/download/index.cgi?P1_Prod_Version=ShockwaveFlash"></embed>'+'</object>';return bM;};function Aw(el){var w=el.width;var h=el.height;var fW=115;var gU=115;var sw,fM;if((w>fW)||(h>gU)){var nw=fW/w;var iK=gU/h;if(nw>iK){fM=gU;sw=w*iK;}else{sw=fW;fM=h*nw;}}else{sw=w;fM=h;}el.style.width=sw;el.style.height=fM;};function yg(){var v=$("d_view").options[$("d_view").selectedIndex].value;qz("BrowseView",v);wM=v;lv();};function wl(){var vT=$("d_sort").options[$("d_sort").selectedIndex].value;var jj=$("d_sortward").options[$("d_sortward").selectedIndex].value;qz("BrowseSort",vT);qz("BrowseSortWard",jj);il=vT+jj;lv();};function rn(flag){var jg=il.substr(0,4);var pv=il.substr(4);var wZ=flag;var jj="";if(flag==jg){if(pv=="desc"){jj="asc";}else{jj="desc";}}else{jj="asc"}aC($("d_sort"),wZ);aC($("d_sortward"),jj);il=wZ+jj;lv();};function qI(s,o){var el=$(s);if(el.style.display=="none"){el.style.display="";o.src=o.src.replace(/plus/g,"minus");}else{el.style.display="none";o.src=o.src.replace(/minus/g,"plus");}};function iM(url){var gA;gA=url.substr(url.lastIndexOf(".")+1);gA=gA.toUpperCase();var aL;switch(gA){case "TXT":aL="txt.gif";break;case "CHM":aL="chm.gif";break;case "HLP":aL="hlp.gif";break;case "DOC":case "DOCX":aL="doc.gif";break;case "PDF":aL="pdf.gif";break;case "MDB":aL="mdb.gif";break;case "GIF":aL="gif.gif";break;case "JPG":case "JPEG":aL="jpg.gif";break;case "BMP":aL="bmp.gif";break;case "PNG":aL="png.gif";break;case "ICO":aL="ico.gif";break;case "ASP":case "JSP":case "PHP":case "PHP3":aL="code.gif";break;case "JS":case "VBS":aL="js.gif";break;case "ASPX":aL="aspx.gif";break;case "XML":aL="xml.gif";break;case "HTM":case "HTML":case "SHTML":aL="htm.gif";break;case "ZIP":aL="zip.gif";break;case "RAR":aL="rar.gif";break;case "EXE":aL="exe.gif";break;case "AVI":aL="avi.gif";break;case "MPG":case "MPEG":case "ASF":aL="mp.gif";break;case "RA":case "RM":aL="rm.gif";break;case "MP3":aL="mp3.gif";break;case "MID":case "MIDI":aL="mid.gif";break;case "WAV":aL="audio.gif";break;case "XLS":aL="xls.gif";break;case "PPT":case "PPS":aL="ppt.gif";break;case "SWF":aL="swf.gif";break;default:aL="unknow.gif";break;}return aL;};function ok(){if(!ip){alert(lang["DlgBrowseNoSelected"]);return;}var url=so+ip;if(config.BaseUrl!="3"){url=gh(url);}else{if(url.substring(0,5)=="share"){url=gh(url);}}if(wo){dP.$("d_"+wo).value=url;try{dP.eT()}catch(e){}}else{var html;switch(gX){case "image":html="<img id='eWebEditor_TempElement_Img' border=0 src='"+url+"'>";EWIN.insertHTML(html);var rd=EWEB.T.getElementById("eWebEditor_TempElement_Img");rd.src=url;rd.removeAttribute("id");break;case "flash":html=wF(url,200,200);EWIN.insertHTML(html);break;case "media":html='<EMBED src="'+url+'" width="200" height="200" type="audio/x-pn-realaudio-plugin" autostart="true" controls="IMAGEWINDOW,ControlPanel,StatusBar" console="Clip1"></EMBED>';EWIN.insertHTML(html);break;default:var oK=iM(ip);var xr=gh("sysimage/icon16/"+oK);EWIN.insertHTML("<img id=eWebEditor_TempElement_Img border=0 src='"+xr+"'><a id=eWebEditor_TempElement_Href href='"+url+"' target=_blank>"+ip+"</a>");var hG=EWEB.T.getElementById("eWebEditor_TempElement_Img");hG.src=xr;hG.removeAttribute("id");hG=EWEB.T.getElementById("eWebEditor_TempElement_Href");hG.href=url;hG.removeAttribute("id");break;}}parent.bV();};function aq(){lang.ag(document);var Bu=pR("BrowsePerPageNum");if(!Bu){Bu=Cy;}aC($("d_perpagenum"),Bu);var vT=pR("BrowseSort");if(!vT){vT=il.substr(0,4);}var jj=pR("BrowseSortWard");if(!jj){jj=il.substr(4);}il=vT+jj;aC($("d_sort"),vT);aC($("d_sortward"),jj);var Be=pR("BrowseView");if(Be){wM=Be;}aC($("d_view"),wM);parent.ar(bm);xg();} </script> <style> div.nodediv{padding:0px;margin:0px;WHITE-SPACE:nowrap;}div.node{padding:0px;margin:0px;WHITE-SPACE:nowrap;}.nodetext{color:#000000;background-color:#ffffff}.nodetextselected{color:#ffffff;background-color:#0A246A}.thumbnailitem{color:#000000;background-color:#ffffff}.thumbnailitemselected{color:#ffffff;background-color:#0A246A}.listitem{color:#000000;background-color:#ffffff}.listitemselected{color:#ffffff;background-color:#0A246A}.iconitem{color:#000000;background-color:#ffffff}.iconitemselected{color:#ffffff;background-color:#0A246A}.detailout{}.detailover{background-color:#eeeeee}.detailover td{color:#000000;}.detailselected{background-color:#0A246A}.detailselected td{color:#ffffff;} </style> </HEAD> <BODY onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 width="100%" align=center> <tr><td> <table border=0 cellpadding=0 cellspacing=0 width="100%" align=center> <tr> <td noWrap width="20px"><IMG src="images/tree/folderopen.gif" border=0></td> <td noWrap width="100%"><SPAN id=d_folderpath>/</SPAN></td> <td noWrap width="" align="right"> <table border=0 cellpadding=0 cellspacing=0> <tr> <td noWrap><span lang="DlgBrowsePage"></span>:</td> <td><select id=d_perpagenum size=1 style="width:50px" onchange="yP()"><option value="all" lang="DlgBrowsePageAll"></option><option value="20" selected>20</option><option value="40">40</option><option value="60">60</option><option value="80">80</option><option value="100">100</option></select></td> <td>&nbsp;</td> <td noWrap><span lang="DlgBrowseSort"></span>:</td> <td><select id=d_sort size=1 style="width:80px" onchange="wl()"><option value="name" lang="DlgBrowseSortName"><option value="size" lang="DlgBrowseSortSize"><option value="type" lang="DlgBrowseSortType"><option value="time" lang="DlgBrowseSortTime"></select></td> <td><select id=d_sortward size=1 style="width:50px" onchange="wl()"><option value="asc" lang="DlgBrowseSortASC"><option value="desc" lang="DlgBrowseSortDESC"></select></td> <td>&nbsp;</td> <td noWrap><span lang="DlgBrowseView"></span>:</td> <td><select id=d_view size=1 style="width:80px" onchange="yg()"><option value="detail" lang="DlgBrowseViewDetails"><option value="thumbnail" lang="DlgBrowseViewThumbnails" selected><option value="icon" lang="DlgBrowseViewIcons"><option value="list" lang="DlgBrowseViewList"></select></td> </table> </td> </tr> </table> </td></tr> <tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td vAlign=top noWrap> <DIV id=divFolder style="BORDER-RIGHT:1.5pt inset;PADDING-RIGHT:0px;BORDER-TOP:1.5pt inset;PADDING-LEFT:0px;PADDING-BOTTOM:0px;OVERFLOW:auto;BORDER-LEFT:1.5pt inset;WIDTH:150px;PADDING-TOP:0px;BORDER-BOTTOM:1.5pt inset;HEIGHT:350px;BACKGROUND-COLOR:white"> </DIV> </td> <td width=10>&nbsp; </td> <td vAlign=top> <DIV id=divFile style="BORDER-RIGHT:1.5pt inset;PADDING-RIGHT:0px;BORDER-TOP:1.5pt inset;PADDING-LEFT:0px;PADDING-BOTTOM:0px;VERTICAL-ALIGN:top;OVERFLOW:auto;BORDER-LEFT:1.5pt inset;WIDTH:540px;PADDING-TOP:0px;BORDER-BOTTOM:1.5pt inset;HEIGHT:350px;BACKGROUND-COLOR:white"> </DIV> </td> </tr> <tr><td colspan=3 height=5></td></tr> <tr> <td></td> <td></td> <td> <table border=0 cellpadding=0 cellspacing=0 width="100%"> <tr> <td id=d_pagenav></td> <td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel></td> </tr> </table> </td> </tr> </table> </td></tr> </table> </td></tr></table> <iframe id=myIframe src="blank.htm" width="0" height="0" frameborder=0></iframe> </body></html>