<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript" src="../template/template.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var bm=lang["DlgTemplate"];document.write("<title>"+bm+"</title>");function ok(){var K=$("iframe_preview").contentWindow.document.body.getElementsByTagName("IMG");var l=K.length;for(var i=0;i<l;i++){var s=K[i].getAttribute("src",2);if((s.substring(0,1)!="/")&&(s.indexOf("://")== -1)){if(s.substring(0,3)=="../"){s=s.substr(3);}else{s="template/"+s;}s=gh(s);}K[i].setAttribute("eWebEditor_Temp_Img_Url",s);}var html=$("iframe_preview").contentWindow.document.body.innerHTML;if(sb=="replace"){EWIN.setHTML(html,true);}else{EWIN.insertHTML(html);}if(l>0){var lA=EWEB.T.getElementsByTagName("IMG");for(var i=0;i<lA.length;i++){var url=lA[i].getAttribute("eWebEditor_Temp_Img_Url",2);if(url){bq(lA[i],"src",url);nP(lA[i],"src",url);lA[i].removeAttribute("eWebEditor_Temp_Img_Url");}}}parent.bV();};function aq(){lang.ag(document);zz();wJ(0);parent.ar(bm);};function zz(){var html="";for(var i=0;i<config.Template.length;i++){html+="<div id='item_"+i+"' class='item' onclick='wJ("+i+")'>"+config.Template[i][2]+"</div>";}$("divOption").innerHTML=html;};var pD= -1;function wJ(n){if(pD==n){return;}var el;if(pD!= -1){el=document.getElementById("item_"+pD);el.className="item";}el=document.getElementById("item_"+n);el.className="itemselected";$("iframe_preview").contentWindow.location.replace("../template/"+config.Template[n][1]);pD=n;};function yU(){try{if(F.as){$("iframe_preview").contentWindow.document.body.runtimeStyle.zoom="50%";}else{}}catch(e){}};var sb="dB";function wk(an){if(sb==an){return;}if(an=="dB"){$("d_pos_insert").checked=true;$("d_pos_replace").checked=false;}else{$("d_pos_insert").checked=false;$("d_pos_replace").checked=true;}sb=an;} </script> <style> .item{padding:0px 0px 0px 5px;margin:0px;WHITE-SPACE:nowrap;color:#000000;background-color:#ffffff}.itemselected{padding:0px 0px 0px 5px;margin:0px;WHITE-SPACE:nowrap;color:#ffffff;background-color:#0A246A} </style> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 width="100%" align=center> <tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td><span lang="DlgTemplateOption"></span>:</td> <td></td> <td><span lang="DlgTemplatePreview"></span>:</td> </tr> <tr> <td vAlign=top noWrap> <DIV id=divOption style="BORDER-RIGHT:1.5pt inset;PADDING-RIGHT:0px;BORDER-TOP:1.5pt inset;PADDING-LEFT:0px;PADDING-BOTTOM:0px;OVERFLOW:auto;BORDER-LEFT:1.5pt inset;WIDTH:150px;PADDING-TOP:0px;BORDER-BOTTOM:1.5pt inset;HEIGHT:310px;BACKGROUND-COLOR:white"> </DIV> </td> <td width=10>&nbsp; </td> <td vAlign=top> <DIV id=divPreview style="BORDER:1.5pt inset;PADDING:0px;VERTICAL-ALIGN:top;OVERFLOW:hidden;WIDTH:410px;HEIGHT:310px;BACKGROUND-COLOR:white"> <iframe id=iframe_preview src="blank.htm" width="100%" height="100%" onload="yU()" frameborder=0 scrolling="auto"></iframe> </DIV> </td> </tr> <tr> <td colspan=3><input type=radio checked id=d_pos_insert onclick="wk('dB')"><label for=d_pos_insert><span lang=DlgComInsertCursor></span></label>&nbsp; <input type=radio id=d_pos_replace onclick="wk('replace')"><label for=d_pos_replace><span lang=DlgComInsertReplace></span></label></td> </tr> </table> </td></tr> <tr><td height=5></td></tr> <tr><td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel></td></tr> </td></tr></table> </body></html>