<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var FE=(((config.SYWZFlag=="2"||config.SYTPFlag=="2")&&config.SYVLocal=="1")?true:false);var bm=lang["DlgPaste"];document.write("<title>"+bm+"</title>");var iw=cc["flag"].split("|");var jy=cc["autodone"];function ok(){if(iw[4]=="1"){if($("d_opthtml").checked){var V=FT.kI.GetClipboard("html");wb(V);return;}}if(iw[1]=="1"){if($("d_opttext").checked){var V=EWIN.fn(FT.kI.GetClipboard("text"));wb(V);return;}}if(FT.FD()){return;}if(!FT.FV(true)){FT.jT(false);return;}$("divProcessing").style.display="";if(iw[3]=="1"){if($("d_optfile").checked){if(FE){if($("d_syflag3").checked){FT.kI.SendUrl=EWEB.SendUrl+"&syflag=1";}}var bu="";FT.kI.PasteFileList(bu);}}if(iw[0]=="1"){if($("d_optimg").checked){if(FE){if($("d_syflag0").checked){FT.kI.SendUrl=EWEB.SendUrl+"&syflag=1";}}var eL="";if($("d_imgjpg").checked){eL="jpg";}else if($("d_imggif").checked){eL="gif";}else if($("d_imgpng").checked){eL="png";}var bu="imgtype:"+eL+";";FT.kI.PasteImage(bu);}}window.setTimeout(hT,300);};function hT(){if(FT.kI.Status!="ok"){window.setTimeout(hT,300);return;}if(FT.Fi()){$("divProcessing").style.display="none";FT.jT(false);return;}EWIN.addUploadFiles(FT.kI.OriginalFiles,FT.kI.SavedFiles);$("divProcessing").style.display="none";var bC=FT.kI.Body;wb(bC);};function wb(V){if($("d_pos").checked){EWIN.setHTML(V,true);}else{EWIN.insertHTML(V);}parent.bV();};function pW(b){try{$("d_imggif").disabled=b}catch(e){};try{$("d_imgjpg").disabled=b}catch(e){};try{$("d_imgpng").disabled=b}catch(e){};};function uc(){if(jy){window.setTimeout("ok()",100);}};function aq(){lang.ag(document);parent.ar(bm);kx();if(jy){ok();}};function kx(){var el=$("divProcessing");el.style.left=($("tabDialogSize").offsetWidth+6-parseInt(el.style.width))/2+"px";el.style.top=($("tabDialogSize").offsetHeight-parseInt(el.style.height))/2+"px";} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td> <fieldset> <legend><span lang=DlgPasteOpt></span>:</legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td valign=top> <table border=0 cellpadding=0 cellspacing=3> <script type="text/javascript"> var Gq=false;var Gy="";if(iw[3]=="1"){Gy=Gq?"":" checked";Gq=true;document.write("<tr><td noWrap>");document.write("<input type=radio id='d_optfile' name='d_opt' "+Gy+" onclick='pW(true)'><label for=d_optfile><span lang=DlgPasteOptFile></span></label><br>&nbsp;&nbsp;<span lang=DlgPasteFileExt></span>:"+config.AllowLocalExt+"<br>&nbsp;&nbsp;<span lang=DlgPasteFileSize></span>:"+wR(config.AllowLocalSize));if(FE){document.write("<br>&nbsp;&nbsp;<span lang=DlgImgSYFlag></span>: <input type=checkbox id=d_syflag3 value='1'>");}document.write("</td></tr>");}if(iw[0]=="1"){Gy=Gq?"":" checked";Gq=true;document.write("<tr><td noWrap>");document.write("<input type=radio id='d_optimg' name='d_opt' "+Gy+" onclick='pW(false)'><label for=d_optimg><span lang=DlgPasteOptImg></span>:</label> <input type=radio id=d_imggif name=d_imgtype><label for=d_imggif>GIF</label> <input type=radio id=d_imgjpg name=d_imgtype><label for=d_imgjpg>JPG</label> <input type=radio id=d_imgpng name=d_imgtype checked><label for=d_imgpng>PNG</label>");if(FE){document.write("<br>&nbsp;&nbsp; <span lang=DlgImgSYFlag></span>: <input type=checkbox id=d_syflag0 value='1'>");}document.write("</td></tr>");}if(iw[4]=="1"){Gy=Gq?"":" checked";Gq=true;document.write("<tr>");document.write("	<td noWrap><input type=radio id='d_opthtml' name='d_opt' "+Gy+" onclick='pW(true)'><label for=d_opthtml><span lang=DlgPasteOptHtml></span></label></td>");document.write("</tr>");}if(iw[1]=="1"){Gy=Gq?"":" checked";Gq=true;document.write("<tr>");document.write("	<td noWrap><input type=radio id='d_opttext' name='d_opt' "+Gy+" onclick='pW(true)'><label for=d_opttext><span lang=DlgPasteOptText></span></label></td>");document.write("</tr>");} </script> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <table border=0 cellpadding=0 cellspacing=0 width="100%"> <tr> <td noWrap><input type=checkbox id=d_pos><label for=d_pos><span lang=DlgComInsertReplace></span></label></td> <td noWrap align=right>&nbsp; <input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel> </tr> </table> </td> </tr> </table> </td></tr></table> <div id=divProcessing style="width:200px;height:30px;position:absolute;left:50px;top:30px;display:none"> <table border=0 cellpadding=0 cellspacing=1 bgcolor="#000000" width="100%" height="100%"><tr><td bgcolor=#3A6EA5 align=center><marquee align="middle" behavior="alternate" scrollamount="5"><font color=#FFFFFF><span lang=DlgPasteFileUploading></span></font></marquee></td></tr></table> </div> </body> </html> 