<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var mx=lC("type","image").toLowerCase();var wS=lC("mode","2");var pQ=lC("savepathfilename","");var qE=lC("savefilename","");var qQ=lC("originalfilename","");var zT=lC("returnflag","2");var cZ="";var aa,pd,kV,jV,cu;switch(mx){case "image":aa=lang["DlgIUploadImage"];pd=lang["DlgImgSource"];kV=config.AllowImageExt;jV=config.AllowImageSize;break;case "flash":aa=lang["DlgIUploadFlash"];pd=lang["DlgFlashSource"];kV=config.AllowFlashExt;jV=config.AllowFlashSize;break;case "media":aa=lang["DlgIUploadMedia"];pd=lang["DlgMediaSource"];kV=config.AllowMediaExt;jV=config.AllowMediaSize;break;default:mx="file";aa=lang["DlgIUploadFile"];pd=lang["DlgFileSource"];kV=config.AllowFileExt;jV=config.AllowFileSize;break;}cu=((parseFloat(jV)>0)?true:false);aa=lang["DlgIUpload"]+"("+aa+")";var bm=aa;document.write("<title>"+bm+"</title>");function bP(what){if(what=="url"){$("d_fromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}$("d_checkfromurl").checked=true;if(cu){$("d_checkfromfile").checked=false;$("uploadfile").disabled=true;}}else{$("d_checkfromurl").checked=false;$("uploadfile").disabled=false;$("d_checkfromfile").checked=true;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}}};function UploadError(an){gd();bP('file');$("divProcessing").style.display="none";try{bX($("uploadfile"),md(an,kV,jV));}catch(e){}};function UploadSaved(gS,yr){$("d_fromurl").value=gS;if(yr){cZ=yr;}else{cZ="";}eH();};function eH(){var km=$("d_fromurl").value;var qh=km.substr(km.lastIndexOf("/")+1);var nZ=$("d_filename").value;ep(pQ,km);ep(qE,qh);ep(qQ,nZ);if(cZ){var rF=cZ.substr(cZ.lastIndexOf("/")+1);ep(pQ,cZ);ep(qE,rF);ep(qQ,nZ);}parent.bV({flag:"AfterIUpload",filecount:1,savepathfilename:pQ,savefilename:qE,originalfilename:qQ,type:mx});};function ep(gn,bv){if(!bv){return;}if(gn){try{var iJ=EWIN.parent.document.getElementById(gn);if(!iJ){iJ=EWIN.parent.document.getElementsByName(gn)[0];}if(iJ){if(zT=="1"){iJ.value=bv;}else{iJ.value=(iJ.value)?(iJ.value+"|"+bv):bv;}iJ.onchange();}}catch(e){}}};function ok(){if($("d_checkfromurl").checked){nB($("d_fromurl").value,"/");eH();}else{if(!iV($("uploadfile").value,kV)){UploadError("ext");return false;}nB($("uploadfile").value,"\\");he();$("divProcessing").style.display="";document.myuploadform.submit();}};function he(){$("d_checkfromfile").disabled=true;$("d_checkfromurl").disabled=true;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}$("d_ok").disabled=true;};function gd(){$("d_checkfromfile").disabled=false;$("d_checkfromurl").disabled=false;$("d_fromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}$("d_ok").disabled=false;};function nB(aG,AO){$("d_filename").value=aG.substr(aG.lastIndexOf(AO)+1);};function aq(){lang.ag(document);if(!cu){bP("url");}else{bP("file");}if(wS=="0"||wS=="1"||wS=="2"){$("tab_normal").style.display="";}if(wS=="3"||wS=="4"||wS=="5"||wS=="6"){$("tab_mfu").style.display="";var rh=(wS=="1"||wS=="3"||wS=="5")?"0":"1";iU.Load(mx,$("tab_mfu"),"500px","250px",rh);}parent.ar(bm);if(cu&&document.documentMode==5){var vt=true;$("uploadfile").disabled=vt;$("uploadfile").disabled= !vt;}};function jS(dO,iq,bZ){if(bZ=="tab_mfu"){var rh=(wS=="1"||wS=="3"||wS=="5")?"0":"1";iU.Load(mx,$(bZ),"500px","250px",rh);}if(ck.iy[dO]){parent.pM(ck.iy[dO].Width,ck.iy[dO].Height);}else{parent.pM($("tabDialogSize").offsetWidth,$("tabDialogSize").offsetHeight);}};function MFUReturn(bv){var eG=bv.split("|");var Bv=0;for(var i=0;i<eG.length;i++){var a=eG[i].split("::");if(a.length==3&&a[1]!=""){Bv++;var nZ=a[0].substr(a[0].lastIndexOf("\\")+1);var km=a[1];var qh=km.substr(km.lastIndexOf("/")+1);ep(pQ,km);ep(qE,qh);ep(qQ,nZ);cZ=a[2];if(cZ){var rF=cZ.substr(cZ.lastIndexOf("/")+1);ep(pQ,cZ);ep(qE,rF);ep(qQ,nZ);}}}parent.bV({flag:"AfterIUpload",filecount:Bv,savepathfilename:pQ,savefilename:qE,originalfilename:qQ,type:mx});} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <script type="text/javascript"> if(config.MFUEnable=="1"){if(wS=="1"||wS=="2"){ck.lQ([[lang["DlgComTabNormal"],"tab_normal"],[lang["DlgComTabMFU"],"tab_mfu"]]);}else if(wS=="5"||wS=="6"){ck.lQ([[lang["DlgComTabMFU"],"tab_mfu"],[lang["DlgComTabNormal"],"tab_normal"]]);}} </script> <table id="tab_normal" border=0 cellpadding=0 cellspacing=0 align=center style="display:none"> <tr> <td> <fieldset> <script type="text/javascript"> document.write('<legend><span>'+pd+'</span></legend>'); </script> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=0 width="300px"><tr><td></td></tr></table> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <script type="text/javascript"> if(cu){document.write("<tr>");document.write("<td noWrap width=\"20%\"><input type=radio id=\"d_checkfromfile\" value=\"1\" onclick=\"bP('file')\"><label for=d_checkfromfile>"+lang["DlgFromFile"]+"</label>:</td>");document.write("<td noWrap width=\"80%\">");document.write(lN(mx));document.write("</td>");document.write("</tr>");} </script> <tr> <td noWrap width="20%"><input type=radio id="d_checkfromurl" value="1" onclick="bP('url')"><label for=d_checkfromurl><span lang=DlgFromUrl></span></label>:</td> <td noWrap width="80%"> <script type="text/javascript"> if(config.AllowBrowse=="1"){document.write("<table border=0 cellpadding=0 cellspacing=0 width='100%'><tr><td width='100%'><input type=text id='d_fromurl' style='width:100%' size=20 value='http://'></td><td><input class='dlgBtnBrowse' type=button id='d_browse' onclick=\"nf('"+mx+"','fromurl')\"  value='"+lang["DlgBtnBrowse"]+"' align=absmiddle></td></tr></table>");}else{document.write("<input type=text id='d_fromurl' style='width:100%' size=40 value='http://'>");} </script> </td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr><td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id=d_ok onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel></td></tr> </table> <div id="tab_mfu" style="display:none"></div> </td></tr></table> <div id=divProcessing style="width:200px;height:30px;position:absolute;left:70px;top:30px;display:none"> <table border=0 cellpadding=0 cellspacing=1 bgcolor="#000000" width="100%" height="100%"><tr><td bgcolor=#3A6EA5><marquee align="middle" behavior="alternate" scrollamount="5"><font color=#FFFFFF><span lang=DlgComUploading></span></font></marquee></td></tr></table> </div> <input type=hidden id=d_filename value=""> </body> </html> 