<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var bm=lang["DlgFile"];document.write("<title>"+bm+"</title>");var cu=((parseFloat(config.AllowFileSize)>0)?true:false);var zJ,AT;function bP(what){if(what=="url"){$("d_fromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}$("d_checkfromurl").checked=true;if(cu){$("d_checkfromfile").checked=false;$("uploadfile").disabled=true;}}else{$("d_checkfromurl").checked=false;$("uploadfile").disabled=false;$("d_checkfromfile").checked=true;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}}};function UploadError(an){gd();bP('file');$("divProcessing").style.display="none";try{bX($("uploadfile"),md(an,config.AllowFileExt,config.AllowFileSize));}catch(e){}};function UploadSaved(rj){$("d_fromurl").value=rj;eH();};function eH(){var url=$("d_fromurl").value;var AJ=iM(url);var xw=gh("sysimage/icon16/"+AJ);EWIN.insertHTML("<img border=0 src='"+xw+"'><a href='"+url+"' target=_blank>"+$("d_filename").value+"</a>");parent.bV();};function ok(){if($("d_checkfromurl").checked){nB($("d_fromurl").value,"/");eH();}else{if(!iV($("uploadfile").value,config.AllowFileExt)){UploadError("ext");return false;}nB($("uploadfile").value,"\\");he();$("divProcessing").style.display="";document.myuploadform.submit();}};function he(){$("d_checkfromfile").disabled=true;$("d_checkfromurl").disabled=true;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}$("d_ok").disabled=true;};function gd(){$("d_checkfromfile").disabled=false;$("d_checkfromurl").disabled=false;$("d_fromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}$("d_ok").disabled=false;};function iM(url){var gA;gA=url.substr(url.lastIndexOf(".")+1);gA=gA.toUpperCase();var aL;switch(gA){case "TXT":aL="txt.gif";break;case "CHM":case "HLP":aL="hlp.gif";break;case "DOC":case "DOCX":aL="doc.gif";break;case "PDF":aL="pdf.gif";break;case "MDB":aL="mdb.gif";break;case "GIF":aL="gif.gif";break;case "JPG":aL="jpg.gif";break;case "BMP":aL="bmp.gif";break;case "PNG":aL="png.gif";break;case "ASP":case "JSP":case "JS":case "PHP":case "PHP3":case "ASPX":aL="code.gif";break;case "HTM":case "HTML":case "SHTML":aL="htm.gif";break;case "ZIP":aL="zip.gif";break;case "RAR":aL="rar.gif";break;case "EXE":aL="exe.gif";break;case "AVI":aL="avi.gif";break;case "MPG":case "MPEG":case "ASF":aL="mp.gif";break;case "RA":case "RM":aL="rm.gif";break;case "MP3":aL="mp3.gif";break;case "MID":case "MIDI":aL="mid.gif";break;case "WAV":aL="audio.gif";break;case "XLS":case "XLSX":aL="xls.gif";break;case "PPT":case "PPTX":case "PPS":aL="ppt.gif";break;case "SWF":aL="swf.gif";break;default:aL="unknow.gif";break;}return aL;};function nB(url,mb){$("d_filename").value=url.substr(url.lastIndexOf(mb)+1);};function aq(){lang.ag(document);if(!cu){bP("url");}else{bP("file");}zJ=$("tab_normal").offsetWidth;AT=$("tab_normal").offsetHeight;parent.ar(bm);if(cu&&document.documentMode==5){var vt=true;$("uploadfile").disabled=vt;$("uploadfile").disabled= !vt;}};function jS(dO,iq,bZ){if(bZ=="tab_mfu"){iU.Load("file",$(bZ),"500px","250px");}if(ck.iy[dO]){parent.pM(ck.iy[dO].Width,ck.iy[dO].Height);}else{parent.pM();}};function MFUReturn(bv){var V="";var eG=bv.split("|");for(var i=0;i<eG.length;i++){var a=eG[i].split("::");if(a.length==3&&a[1]!=""){var cf=a[0].substr(a[0].lastIndexOf("\\")+1);var gI=a[1];var oK=iM(gI);oK=gh('sysimage/icon16/'+oK);V+='<img border=0 src="'+oK+'"><a href="'+gI+'" target="_blank">'+cf+'</a><br>';EWIN.addUploadFile(cf,gI);}}EWIN.insertHTML(V);parent.bV();} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <script type="text/javascript"> if(config.MFUEnable=="1"){ck.lQ([[lang["DlgComTabNormal"],"tab_normal"],[lang["DlgComTabMFU"],"tab_mfu"]]);} </script> <table id="tab_normal" border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td> <fieldset> <legend><span lang=DlgFileSource></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=0 width="300px"><tr><td></td></tr></table> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <script type="text/javascript"> if(cu){document.write("<tr>");document.write("<td noWrap width=\"20%\"><input type=radio id=\"d_checkfromfile\" value=\"1\" onclick=\"bP('file')\"><label for=d_checkfromfile>"+lang["DlgFromFile"]+"</label>:</td>");document.write("<td noWrap width=\"80%\">");document.write(lN("file"));document.write("</td>");document.write("</tr>");} </script> <tr> <td noWrap width="20%"><input type=radio id="d_checkfromurl" value="1" onclick="bP('url')"><label for=d_checkfromurl><span lang=DlgFromUrl></span></label>:</td> <td noWrap width="80%"> <script type="text/javascript"> if(config.AllowBrowse=="1"){document.write("<table border=0 cellpadding=0 cellspacing=0 width='100%'><tr><td width='100%'><input type=text id='d_fromurl' style='width:100%' size=20 value='http://'></td><td><input class='dlgBtnBrowse' type=button id='d_browse' onclick=\"nf('file','fromurl')\"  value='"+lang["DlgBtnBrowse"]+"' align=absmiddle></td></tr></table>");}else{document.write("<input type=text id='d_fromurl' style='width:100%' size=40 value='http://'>");} </script> </td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr><td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id=d_ok onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel></td></tr> </table> <div id="tab_mfu" style="display:none"></div> </td></tr></table> <div id=divProcessing style="width:200px;height:30px;position:absolute;left:70px;top:30px;display:none"> <table border=0 cellpadding=0 cellspacing=1 bgcolor="#000000" width="100%" height="100%"><tr><td bgcolor=#3A6EA5><marquee align="middle" behavior="alternate" scrollamount="5"><font color=#FFFFFF><span lang=DlgComUploading></span></font></marquee></td></tr></table> </div> <input type=hidden id=d_filename value=""> </body> </html> 