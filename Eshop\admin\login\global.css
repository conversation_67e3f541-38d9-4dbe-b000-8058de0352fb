﻿/* public elements */
body, center, cite, code, dd, del, div, dl, dt, em, fieldset, font, form, h1, h2, h3, h4, h5, h6, html, i, iframe, img, ins, label, legend, li, ol, p, pre, small, span, strong, u, ul, var { padding: 0; margin: 0; }
ol, ul { list-style: none; }
a { color: #666; text-decoration: none; }
a:hover { color: #E4393C; text-decoration: underline; }
em, i, u { font-style: normal; }
img { border: 0; vertical-align: middle; }
h1 { font: 20px "microsoft yahei", "\5b8b\4f53"; }
h2, h3 { font-family: "microsoft yahei"; font-weight: 400; }
h4, h5, h6 { font-size: 12px; }
.wid { width: 990px; margin: 0 auto; }
.fl { float: left; }
.fr { float: right; }
.clear, .clr { display: block; overflow: hidden; clear: both; height: 0; line-height: 0; font-size: 0; }
.clearfix { display: block; }
.clearfix:after { content: "."; display: block; height: 0; clear: both; visibility: hidden; }
.m, .mb, .mc, .mt, .p-detail, .p-img, .p-market, .p-name, .p-price, .sm, .smb, .smc, .smt { overflow: hidden; zoom: 1; }
.p-price strong, .p-market strong { color: #E4393C; }
.p-name, .p-detail { word-break: break-all; word-wrap: break-word; }
.p-img, .p-name, .p-price, .p-market, .p-detail { overflow: hidden; }
.p-img { padding: 5px 0; }
.form label, .form input, .form select, .form textarea, .form button, .form .label { float: left; font-size: 12px; }
.btn-img, .button { display: inline-block; margin: 0; padding: 0; border: 0; text-align: center; cursor: pointer; }
:focus { outline: 0; }