/* CSS Document */
body { font: 12px/150% <PERSON><PERSON>, <PERSON><PERSON><PERSON>, "\5b8b\4f53"; color: #666; background: #fff; }
#logo { position: relative; width: 300px; margin: 10px 0 0; height: 60px; }
#logo b { display: block; width: 110px; height: 40px; position: absolute; right: 8px; top: 13px; background: url(../../image/l-icon.png) no-repeat; }
.login-wrap { position: relative; height: 475px; margin: 10px 0 20px; }
.login-form { position: relative; z-index: 4; width: 306px; background: #fff; padding: 20px; overflow: visible; }
.login-wrap .login-form { float: right; top: 40px; }
.login-form .login-box .mt { display: inline-block; display: block; width: 306px; overflow: visible; height: 27px; }
.login-form .login-box .mt h1 { position: relative; height: 27px; line-height: 27px; top: -5px; float: left; color: #666; font-family: '\5fae\8f6f\96c5\9ed1'; font-size: 20px; }
.login-form .login-box .mt .extra-r { float: right; }
.login-form .login-box .mt .regist-link { color: #b61d1d; font-size: 14px; }
.login-form .login-box .mt .regist-link a { color: #b61d1d; }
.login-form .login-box .mt .regist-link b { display: inline-block; width: 16px; height: 16px; overflow: hidden; background: url(../../image/pwd-icons-new.png)-104px -75px no-repeat; vertical-align: middle; margin-right: 5px; }
.login-form .msg-wrap { min-height: 31px; height: auto!important; height: 31px; margin: 2px 0 5px; }
.login-form .msg-warn { position: relative; background: #fff6d2; color: #666; border: 1px solid #ffe57d; padding: 3px 10px 3px 40px; line-height: 18px; height: 18px; }
.login-form .msg-warn b { position: absolute; top: 50%; left: 10px; display: block; margin-top: -8px; width: 16px; height: 17px; overflow: hidden; background: url(../../image/pwd-icons-new.png) -104px -22px no-repeat; }
.login-form .msg-error { position: relative; background: #ffebeb; color: #e4393c; border: 1px solid #e4393c; padding: 3px 10px 3px 40px; line-height: 18px; min-height: 18px; _height: 18px; }
.login-form .msg-error b { position: absolute; top: 50%; left: 10px; display: block; margin-top: -8px; width: 16px; height: 16px; overflow: hidden; background: url(../../image/pwd-icons-new.png) -104px -49px no-repeat; }
.form .item { position: relative; margin-bottom: 20px; z-index: 1; }
.form .item-fore1, .form .item-fore2 { border: 1px solid #bdbdbd; height: 38px; width: 304px; }
.form .item-fore1 { z-index: 6; }
.form .item-error { border: 1px solid #e4393c; }
.form .item-focus { border: 1px solid #3aa2e4; }
.form label { float: none; }
.form .item .login-label { position: absolute; z-index: 3; top: 0; left: 0; width: 38px; height: 38px; border-right: 1px solid #bdbdbd; background: url(../../image/pwd-icons-new.png) no-repeat; }
.form .item .name-label { background-position: 0 0; }
.form .item .pwd-label { background-position: -48px 0; }
.form .item-error .name-label { background-position: 0 -96px; border-color: #e4393c; }
.form .item-focus .name-label { background-position: 0 -48px; border-color: #3aa2e4; }
.form .item-error .pwd-label { background-position: -48px -96px; border-color: #e4393c; }
.form .item-focus .pwd-label { background-position: -48px -48px; border-color: #3aa2e4; }
.form .itxt { line-height: 18px; height: 18px; border: 0; padding: 10px 0 10px 50px; width: 254px; float: none; overflow: hidden; font-size: 14px; font-family: '\5b8b\4f53'; }
.form .item-fore2 { height: 38px; }
.form input[type=password] { font-family: Tahoma, Helvetica, Arial; }
.form .item-fore3 { z-index: 5; margin-bottom: 15px; }
.login-form .login-box .safe { position: relative; color: #666; }
.login-form .login-box .safe span { margin-right: 15px; }
.form .checkbox { float: none; vertical-align: middle; _vertical-align: -1px; margin: 0 3px 0 0; padding: 0; }
.login-form .login-box .login-btn { border: 1px solid #cb2a2d; margin: 0 auto; height: 33px; position: relative; }
.login-form .login-box .login-btn .btn-img { border: 1px solid #e85356; display: block; width: 302px; background: #e4393c; height: 31px; line-height: 31px; color: #fff; font-size: 20px; font-family: '\5fae\8f6f\96c5\9ed1'; }
.login-form .login-box .login-btn .btn-img:hover{ text-decoration:none; }
.login-form .login-box .coagent { line-height: 22px; height: auto; }
.login-form .login-box .coagent h5 { font-weight: 400; color: #999; }
.login-form .login-box .coagent ul { display: inline-block; display: block; }
.login-form .login-box .coagent li { float: left; }
.login-form .login-box .coagent li .line { color: #ccc; padding: 0 10px; }
.login-wrap .login-banner { position: absolute; left: 0; top: 0; width: 100%; height: 475px; background: #e93854; }
.login-wrap .i-inner { position: relative; z-index: 3; height: 475px; }