<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aJ=cc["action"];var jy=cc["autodone"];var iB="";if(aJ=="paste"){iB="("+lang["DlgQFPaste"]+")";}var bm=lang["DlgQF"]+iB;document.write("<title>"+bm+"</title>");var wL="";function ok(){if($("d_confusion").checked){if($("d_confusioncolor").checked){$("v_confusioncolor").value=fk($("v_confusioncolor").value);if(iz($("v_confusioncolor").value)==""){return bX($("v_confusioncolor"),lang["ErrColorInvalid"]);}}if($("d_confusionclass").checked){$("v_confusionclass").value=fk($("v_confusionclass").value);if($("v_confusionclass").value==""){return bX($("v_confusionclass"),lang["DlgQFConfusionClass"]+":"+lang["ErrInputNull"]);}}}var xp="";if($("d_addmargintop").checked){$("v_addmargintop").value=fk($("v_addmargintop").value);if($("v_addmargintop").value==""){return bX($("v_addmargintop"),lang["DlgQFAddMarginTop"]+":"+lang["ErrInputNull"]);}var v=$("v_addmargintop").value;if(isNaN(parseFloat(v))){v="0";}if(v!="0"){re=/[^0-9\.]+/gi;if(!re.test(v)){v+="em";}}xp=v;}var xh="";if($("d_addmarginbottom").checked){$("v_addmarginbottom").value=fk($("v_addmarginbottom").value);if($("v_addmarginbottom").value==""){return bX($("v_addmarginbottom"),lang["DlgQFAddMarginBottom"]+":"+lang["ErrInputNull"]);}var v=$("v_addmarginbottom").value;if(isNaN(parseFloat(v))){v="0";}if(v!="0"){re=/[^0-9\.]+/gi;if(!re.test(v)){v+="em";}}xh=v;}if($("d_addlineheight").checked){$("v_addlineheight").value=fk($("v_addlineheight").value);if($("v_addlineheight").value==""){return bX($("v_addlineheight"),lang["DlgQFAddLineHeight"]+":"+lang["ErrInputNull"]);}}if($("d_confusion").checked&&$("d_confusionsuper").checked){if(!FT.FV(true)){return;}FT.kI.QuickFormat();if(FT.Fi()){return;}var yn=FT.kI.Style;var su=FT.kI.Body;if(yn){EWIN.setHTML(yn+EWIN.getHTML(),true);wL=iz(zl(su));}}if($("d_confusion").checked){zN();}if($("d_delhidden").checked||$("d_confusion").checked){ij("hidden");ij("STYLE");ij("SCRIPT");}if(!$("d_keepall").checked){if(!$("d_keeptable").checked){ij("TABLE");}if(!$("d_keepimg").checked){ij("IMG");}if(!$("d_keepobject").checked){ij("OBJECT");ij("EMBED");ij("APPLET");}}var html=EWIN.getHTML();var re=/<!--([^a]|a)*?-->/gi;html=html.replace(re,"");re=/<br\s*\/?>\s*/gi;html=html.replace(re,"<br>");if(!$("d_keepall").checked){var sL=",P,DIV,BR,TABLE,TBODY,TH,TR,TD,IMG,APPLET,OBJECT,EMBED,PARAM,";if($("d_keepul").checked){sL+="UL,OL,LI,";}if($("d_keepa").checked){sL+="A,";}re=/<\/?([a-zA-Z]+)(\s[^>]*)?>/gi;html=html.replace(re,function($0,$1){if(sL.indexOf(","+$1.toUpperCase()+",")> -1){return $0;}else{return "";}});}re=/^[\s\r\n]*[^<\s\r\n]+/gi;if(re.test(html)){html="<p>"+html;}if($("d_addbr2p").checked){re=/<br>/gi;html=html.replace(re,"</p><p>");}if($("d_delspace").checked){re=/(\s|&nbsp\;)+<\/p>/gi;html=html.replace(re,"</p>");re=/(\s|&nbsp\;)+<br>/gi;html=html.replace(re,"<br>");}if($("d_delline").checked){re=/<p(\s[^>]*)?>(&nbsp\;|\s)*<\/p>/gi;html=html.replace(re,"");re=/(<br>)+/gi;html=html.replace(re,"<br>");}if($("d_delallattr").checked){re=/(<[a-zA-Z]+)(\s[^>]*)>/gi;html=html.replace(re,function($0,$1,$2){var r="";var m=$2.match(/(src|href|type|name|value|rowsspan|colspan|classid|codebase)=(\'[^\']+\'|\"[^\"]+\"|[^\s]+)/gi);if(m){for(var i=0;i<m.length;i++){r+=" "+m;}}return $1+r+">";});}else{if($("d_delstyle").checked){re=/(<[^>]+?)(\sstyle\s*=\s*(\'[^\']*?\'|\"[^\"]*?\"))([^>]*>)/gi;html=html.replace(re,"$1$4");re=/(<[^>]+?)(\sclass\s*=\s*(\'[^\']*?\'|\"[^\"]*?\"|\w+))([^>]*>)/gi;html=html.replace(re,"$1$4");}if($("d_delon").checked){re=/(<[^>]+?)(\son[a-zA-Z]+\s*=\s*(\w+\([^\)]*?\)|\'[^\']*?\'|\"[^\"]*?\"))([^>]*)>/gi;html=html.replace(re,"$1$4");}}if($("d_addindent").checked){re=/(<p(\s[^>]*)?>)(&nbsp\;|\s)*/gi;html=html.replace(re,"$1");}if($("d_addtablebc").checked){re=/<table(\s[^>]*)?>/gi;html=html.replace(re,"<table style=\"BORDER-COLLAPSE:collapse\" border=1 bordercolor=\"#000000\" cellpadding=2 cellspacing=0>");}EWIN.setHTML(html,true);var K,el;K=EWEB.T.getElementsByTagName("P");for(var i=0;i<K.length;i++){el=K[i];if($("d_addmargintop").checked){el.style.marginTop=xp;}if($("d_addmarginbottom").checked){el.style.marginBottom=xh;}if($("d_addindent").checked){el.style.textIndent="2em";}if($("d_addjustify").checked){el.style.textAlign="justify";el.style.textJustify="inter-ideograph";el.align="";}if($("d_addlineheight").checked){el.style.lineHeight=$("v_addlineheight").value;}}K=EWEB.T.getElementsByTagName("DIV");for(var i=0;i<K.length;i++){el=K[i];if(yF(el)){continue;}if($("d_addmargintop").checked){el.style.marginTop=xp;}if($("d_addmarginbottom").checked){el.style.marginBottom=xh;}if($("d_addindent").checked){el.style.textIndent="2em";}if($("d_addjustify").checked){el.style.textAlign="justify";el.style.textJustify="inter-ideograph";el.align="";}if($("d_addlineheight").checked){el.style.lineHeight=$("v_addlineheight").value;}}var sP=$("d_addfontname").options[$("d_addfontname").selectedIndex].value;var iZ=$("d_addfontsize").options[$("d_addfontsize").selectedIndex].value;if(sP||iZ){K=EWEB.T.getElementsByTagName("*");for(var i=0;i<K.length;i++){if("|P|DIV|TD|TH|SPAN|FONT|UL|LI|A|".indexOf("|"+K[i].tagName+"|")>=0){if(!yF(K[i])){if(sP){K[i].style.fontFamily=sP;}if(iZ){K[i].style.fontSize=iZ;}}}}}if($("d_addimgcenter").checked){K=EWEB.T.getElementsByTagName("IMG");for(var i=0;i<K.length;i++){el=K[i];el.align="center";C.vR(el);EWEB.T.execCommand("justifycenter",false,null);var p=C.cI();if("|P|DIV|".indexOf("|"+p.tagName+"|")>=0&&p.childNodes.length==1){if(!yF(p)){p.style.textIndent="";p.style.textAlign="center";p.style.textJustify="";p.align="center";}}}C.Empty();EWEB.aR.scrollTo(0,0);}parent.bV({flag:"AfterQuickFormat"});};function yF(el){if(config.FixWidth){if(el.tagName=="DIV"){if(el.id=="eWebEditor_FixWidth_DIV"){return true;}}}return false;};function zN(){uu("SPAN");uu("FONT");uu("DIV");};function uu(aH){while(xz(aH)){}};function xz(aH){var K=EWEB.T.getElementsByTagName(aH);for(var i=0;i<K.length;i++){var el=K[i];if(yF(el)){continue;}if($("d_confusionauto").checked||$("d_confusionsuper").checked){var mI="";if($("d_confusionsuper").checked){mI=iz(uq(el,"BODY"));}else{mI=iz(uq(el,""));}if(!mI){mI=wL;}var iZ=R.ce(el,"fontSize");if(iZ){iZ=iZ.substr(0,1);}if((iz(R.ce(el,"color"))==mI)||(iZ=="0")){cE(el);return true;}}else if($("d_confusioncolor").checked){if(iz(R.ce(el,"color"))==iz($("v_confusioncolor").value)){cE(el);return true;}}else if($("d_confusionclass").checked){if(el.className==$("v_confusionclass").value){cE(el);return true;}}}return false;};function iz(aQ){var xj={"ALICEBLUE":"#F0F8FF","ANTIQUEWHITE":"#FAEBD7","AQUA":"#00FFFF","AQUAMARINE":"#7FFFD4","AZURE":"#F0FFFF","BEIGE":"#F5F5DC","BISQUE":"#FFE4C4","BLACK":"#000000","BLANCHEDALMOND":"#FFEBCD","BLUE":"#0000FF","BLUEVIOLET":"#8A2BE2","BROWN":"#A52A2A","BURLYWOOD":"#DEB887","CADETBLUE":"#5F9EA0","CHARTREUSE":"#7FFF00","CHOCOLATE":"#D2691E","CORAL":"#FF7F50","CORNFLOWERBLUE":"#6495ED","CORNSILK":"#FFF8DC","CRIMSON":"#DC143C","CYAN":"#00FFFF","DARKBLUE":"#00008B","DARKCYAN":"#008B8B","DARKGOLDENROD":"#B8860B","DARKGRAY":"#A9A9A9","DARKGREEN":"#006400","DARKKHAKI":"#BDB76B","DARKMAGENTA":"#8B008B","DARKOLIVEGREEN":"#556B2F","DARKORANGE":"#FF8C00","DARKORCHID":"#9932CC","DARKRED":"#8B0000","DARKSALMON":"#E9967A","DARKSEAGREEN":"#8FBC8F","DARKSLATEBLUE":"#483D8B","DARKSLATEGRAY":"#2F4F4F","DARKTURQUOISE":"#00CED1","DARKVIOLET":"#9400D3","DEEPPINK":"#FF1493","DEEPSKYBLUE":"#00BFFF","DIMGRAY":"#696969","DODGERBLUE":"#1E90FF","FIREBRICK":"#B22222","FLORALWHITE":"#FFFAF0","FORESTGREEN":"#228B22","FUCHSIA":"#FF00FF","GAINSBORO":"#DCDCDC","GHOSTWHITE":"#F8F8FF","GOLD":"#FFD700","GOLDENROD":"#DAA520","GRAY":"#808080","GREEN":"#008000","GREENYELLOW":"#ADFF2F","HONEYDEW":"#F0FFF0","HOTPINK":"#FF69B4","INDIANRED":"#CD5C5C","INDIGO":"#4B0082","IVORY":"#FFFFF0","KHAKI":"#F0E68C","LAVENDER":"#E6E6FA","LAVENDERBLUSH":"#FFF0F5","LAWNGREEN":"#7CFC00","LEMONCHIFFON":"#FFFACD","LIGHTBLUE":"#ADD8E6","LIGHTCORAL":"#F08080","LIGHTCYAN":"#E0FFFF","LIGHTGOLDENRODYELLOW":"#FAFAD2","LIGHTGREEN":"#90EE90","LIGHTGREY":"#D3D3D3","LIGHTPINK":"#FFB6C1","LIGHTSALMON":"#FFA07A","LIGHTSEAGREEN":"#20B2AA","LIGHTSKYBLUE":"#87CEFA","LIGHTSLATEGRAY":"#778899","LIGHTSTEELBLUE":"#B0C4DE","LIGHTYELLOW":"#FFFFE0","LIME":"#00FF00","LIMEGREEN":"#32CD32","LINEN":"#FAF0E6","MAGENTA":"#FF00FF","MAROON":"#800000","MEDIUMAQUAMARINE":"#66CDAA","MEDIUMBLUE":"#0000CD","MEDIUMORCHID":"#BA55D3","MEDIUMPURPLE":"#9370DB","MEDIUMSEAGREEN":"#3CB371","MEDIUMSLATEBLUE":"#7B68EE","MEDIUMSPRINGGREEN":"#00FA9A","MEDIUMTURQUOISE":"#48D1CC","MEDIUMVIOLETRED":"#C71585","MIDNIGHTBLUE":"#191970","MINTCREAM":"#F5FFFA","MISTYROSE":"#FFE4E1","MOCCASIN":"#FFE4B5","NAVAJOWHITE":"#FFDEAD","NAVY":"#000080","OLDLACE":"#FDF5E6","OLIVE":"#808000","OLIVEDRAB":"#6B8E23","ORANGE":"#FFA500","ORANGERED":"#FF4500","ORCHID":"#DA70D6","PALEGOLDENROD":"#EEE8AA","PALEGREEN":"#98FB98","PALETURQUOISE":"#AFEEEE","PALEVIOLETRED":"#DB7093","PAPAYAWHIP":"#FFEFD5","PEACHPUFF":"#FFDAB9","PERU":"#CD853F","PINK":"#FFC0CB","PLUM":"#DDA0DD","POWDERBLUE":"#B0E0E6","PURPLE":"#800080","RED":"#FF0000","ROSYBROWN":"#BC8F8F","ROYALBLUE":"#4169E1","SADDLEBROWN":"#8B4513","SALMON":"#FA8072","SANDYBROWN":"#F4A460","SEAGREEN":"#2E8B57","SEASHELL":"#FFF5EE","SIENNA":"#A0522D","SILVER":"#C0C0C0","SKYBLUE":"#87CEEB","SLATEBLUE":"#6A5ACD","SLATEGRAY":"#708090","SNOW":"#FFFAFA","SPRINGGREEN":"#00FF7F","STEELBLUE":"#4682B4","TAN":"#D2B48C","TEAL":"#008080","THISTLE":"#D8BFD8","TOMATO":"#FF6347","TURQUOISE":"#40E0D0","VIOLET":"#EE82EE","WHEAT":"#F5DEB3","WHITE":"#FFFFFF","WHITESMOKE":"#F5F5F5","YELLOW":"#FFFF00","YELLOWGREEN":"#9ACD32"};if(!aQ){return "";}aQ=aQ.toUpperCase();var re;re=/#[0-9A-H]{6}/gi;if(re.test(aQ)){return aQ;}re=/#[0-9A-H]{3}/gi;if(re.test(aQ)){var ab=aQ.substr(1,1);var aF=aQ.substr(2,1);var vM=aQ.substr(3,1);return "#"+ab+ab+aF+aF+vM+vM;}re=/[A-Z]+/gi;if(re.test(aQ)){if(xj[aQ]){return xj[aQ];}}re=/rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/gi;if(re.test(aQ)){var Bp=aQ.split(/\D+/);var Db=Number(Bp[1])*65536+Number(Bp[2])*256+Number(Bp[3]);var ky=Db.toString(16);while(ky.length<6){ky="0"+ky;}return "#"+ky.toUpperCase();}return "";};function zl(su){var doc=$("ifr_temp").contentWindow.document;doc.open();doc.write(su);doc.close();var el=doc.getElementById("eWebEditor_Temp_Span");return uq(el,"");};function uq(el,zh){var b=true;while(b){el=el.parentElement;if(!el){return "";}if(el.tagName==zh){return "";}var tg=R.ce(el,"backgroundColor");if((tg)&&(tg!="transparent")){return tg;}}return "";};function ij(flag){var b=false;var dK;if(config.FixWidth){dK=EWEB.T.getElementById("eWebEditor_FixWidth_DIV");}else{dK=EWEB.T.body;}while(!b){b=wu(dK,flag);}};function wu(obj,flag){var oo=obj.children;for(var i=0;i<oo.length;i++){var o=oo[i];if(flag=="hidden"&&((R.ce(o,"display")=="none")||(R.ce(o,"visibility")=="hidden"))){cE(o);return false;}else if(o.tagName==flag){if(flag=="TABLE"&&$("d_keepimg").checked){var lA=o.getElementsByTagName("IMG");for(var j=0;j<lA.length;j++){o.parentNode.insertBefore(lA[j],o);}}cE(o);return false;}else{if(!wu(o,flag)){return false;}}}return true;};function cE(obj){obj.parentNode.removeChild(obj);};function zn(obj){if(obj.checked){$("d_keepul").checked=true;$("d_keeptable").checked=true;$("d_keepimg").checked=true;$("d_keepobject").checked=true;$("d_keepa").checked=true;$("d_keepul").disabled=true;$("d_keeptable").disabled=true;$("d_keepimg").disabled=true;$("d_keepobject").disabled=true;$("d_keepa").disabled=true;}else{$("d_keepul").disabled=false;$("d_keeptable").disabled=false;$("d_keepimg").disabled=false;$("d_keepobject").disabled=false;$("d_keepa").disabled=false;}};function zx(obj){if(obj.checked){$("d_delstyle").checked=true;$("d_delon").checked=true;$("d_delstyle").disabled=true;$("d_delon").disabled=true;}else{$("d_delstyle").disabled=false;$("d_delon").disabled=false;}};function AE(obj){var b=(!obj.checked);$("d_confusionauto").disabled=b;$("d_confusionsuper").disabled=b;$("d_confusioncolor").disabled=b;$("v_confusioncolor").disabled=b;$("d_confusionclass").disabled=b;$("v_confusionclass").disabled=b;};function uc(){if(jy){window.setTimeout("ok()",100);}};function aq(){var bW="";for(var k=0;k<lang["FontNameItem"].length;k++){bW+="<option value='"+lang["FontNameItem"][k]+"'";var EE=lang["FontNameItem"][k].split(",")[0];if(EE==config.QFIFontName){bW+=" selected";}bW+=">"+EE+"</option>";}td_addfontfamily.innerHTML="<select id=d_addfontname size=1 style='width:100px'><option selected value=''>"+lang["FontName"]+"</option>"+bW+"</select>";bW="";for(var k=0;k<lang["FontSizeItem"].length;k++){bW+="<option value='"+lang["FontSizeItem"][k][0]+"'";if(lang["FontSizeItem"][k][1]==config.QFIFontSize){bW+=" selected";}bW+=">"+lang["FontSizeItem"][k][1]+"</option>";}td_addfontsize.innerHTML="<select id=d_addfontsize size=1 style='width:100px'><option selected value=''>"+lang["FontSize"]+"</option>"+bW+"</select>";if(aJ=="paste"){$("d_confusionsuper").checked=true;}lang.ag(document);parent.ar(bm);if(jy){ok();}} </script> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td> <fieldset> <legend><span lang=DlgQFKeep></span>:</legend> <table border=0 cellpadding=3 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2> <tr><td noWrap><input type=checkbox id=d_keepall onclick="zn(this)"><label for=d_keepall><span lang=DlgQFKeepAll></span></label></td></tr> <tr><td noWrap><input type=checkbox id=d_keepul checked><label for=d_keepul><span lang=DlgQFKeepUl></span></label></td></tr> <tr><td noWrap><input type=checkbox id=d_keeptable checked><label for=d_keeptable><span lang=DlgQFKeepTable></span></label></td></tr> <tr><td noWrap><input type=checkbox id=d_keepimg checked><label for=d_keepimg><span lang=DlgQFKeepImg></span></label></td></tr> <tr><td noWrap><input type=checkbox id=d_keepobject checked><label for=d_keepobject><span lang=DlgQFKeepObject></span></label></td></tr> <tr><td noWrap><input type=checkbox id=d_keepa checked><label for=d_keepa><span lang=DlgQFKeepA></span></label></td></tr> </table> </td></tr> </table> </fieldset> </td> <td width=5>&nbsp;</td> <td> <fieldset> <legend><span lang=DlgQFDel></span>:</legend> <table border=0 cellpadding=3 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2> <tr><td noWrap><input type=checkbox id=d_delline checked><label for=d_delline><span lang=DlgQFDelLine></span></label></td></tr> <tr><td noWrap><input type=checkbox id=d_delspace checked><label for=d_delspace><span lang=DlgQFDelSpace></span></label></td></tr> <tr><td noWrap><input type=checkbox id=d_delhidden checked><label for=d_delhidden><span lang=DlgQFDelHidden></span></label></td></tr> <tr><td noWrap><input type=checkbox id=d_delallattr onclick="zx(this)"><label for=d_delallattr><span lang=DlgQFDelAllAttr></span></label></td></tr> <tr><td noWrap><input type=checkbox id=d_delstyle checked><label for=d_delstyle><span lang=DlgQFDelStyle></span></label></td></tr> <tr><td noWrap><input type=checkbox id=d_delon checked><label for=d_delon><span lang=DlgQFDelOn></span></label></td></tr> </table> </td></tr> </table> </fieldset> </td> <td width=5>&nbsp;</td> <td> <fieldset> <legend><span lang=DlgQFAdd></span>:</legend> <table border=0 cellpadding=3 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2> <tr> <td noWrap><input type=checkbox id=d_addmargintop checked><label for=d_addmargintop><span lang=DlgQFAddMarginTop></span></label><input type=text id=v_addmargintop size=3 value="0"></td> <td rowspan=6>&nbsp;</td> <td noWrap><input type=checkbox id=d_addindent checked><label for=d_addindent><span lang=DlgQFAddIndent></span></label></td> <td rowspan=6>&nbsp;</td> <td noWrap><input type=checkbox id=d_confusion checked onclick="AE(this)"><label for=d_confusion><span lang=DlgQFConfusion></span></label></td> </tr> <tr> <td noWrap><input type=checkbox id=d_addmarginbottom checked><label for=d_addmarginbottom><span lang=DlgQFAddMarginBottom></span></label><input type=text id=v_addmarginbottom size=3 value="0"></td> <td noWrap><input type=checkbox id=d_addjustify checked><label for=d_addjustify><span lang=DlgQFAddJustify></span></label></td> <td noWrap><input type=radio id=d_confusionauto name=d_confusionopt checked><label for=d_confusionauto><span lang=DlgQFConfusionAuto></span></label></td> </tr> <tr> <td noWrap><input type=checkbox id=d_addlineheight><label for=d_addlineheight><span lang=DlgQFAddLineHeight></span></label><input type=text id=v_addlineheight size=3 value="1.5"></td> <td noWrap><input type=checkbox id=d_addbr2p><label for=d_addbr2p><span lang=DlgQFAddBR2P></span></label></td> <td noWrap><input type=radio id=d_confusionsuper name=d_confusionopt><label for=d_confusionsuper><span lang=DlgQFConfusionSuper></span></label></td> </tr> <tr> <td noWrap id="td_addfontfamily"></td> <td noWrap><input type=checkbox id=d_addtablebc><label for=d_addtablebc><span lang=DlgQFAddTableBC></span></label></td> <td noWrap><input type=radio id=d_confusioncolor name=d_confusionopt><label for=d_confusioncolor><span lang=DlgQFConfusionColor></span></label><input type=text id=v_confusioncolor size=6 value="#FFFFFF"></td> </tr> <tr> <td noWrap id="td_addfontsize"></td> <td noWrap><input type=checkbox id=d_addimgcenter><label for=d_addimgcenter><span lang=DlgQFAddImgCenter></span></label></td> <td noWrap><input type=radio id=d_confusionclass name=d_confusionopt><label for=d_confusionclass><span lang=DlgQFConfusionClass></span></label><input type=text id=v_confusionclass size=6></td> </tr> <tr> <td noWrap>&nbsp;</td> <td noWrap></td> <td noWrap></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td colspan=5 height=5></td></tr> <tr> <td colspan=5> <table border=0 cellpadding=0 cellspacing=0 width="100%"> <tr> <td noWrap></td> <td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel> </tr> </table> </td> </tr> </table> </td></tr></table> <div style="display:none"> <iframe id=ifr_temp frameborder='0' src='blank.htm'></iframe> </div> </body> </html> 