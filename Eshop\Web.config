﻿<?xml version="1.0"?>
<configuration>
  <configSections>
    <!--Url重写配置参数-->
    <section name="RewriterConfig" type="URLRewriter.Config.RewriterConfigSerializerSection<PERSON>and<PERSON>, URLRewriter"/>
    <!--登录接口配置参数-->
    <sectionGroup name="SinaSectionGroup">
      <section name="SinaSection" type="System.Configuration.NameValueSectionHandler,System, Version=4.0.0.0, Culture=neutral,PublicKeyToken=b77a5c561934e089"/>
    </sectionGroup>
    <sectionGroup name="QQSectionGroup">
      <section name="QzoneSection" type="System.Configuration.NameValueSectionHandler,System, Version=4.0.0.0, Culture=neutral,PublicKeyToken=b77a5c561934e089"/>
    </sectionGroup>
  </configSections>
  <!--微博登录接口参数-->
  <SinaSectionGroup>
    <SinaSection>
      <add key="AppKey" value="1979566217"/>
      <add key="AppSecret" value="f1304074fc624b04ff309be49e774108"/>
      <add key="CallBackURI" value="http://wigz.net/Apply/outside/weibo.aspx"/>
      <add key="OAuthType" value="HttpHeader"/>
    </SinaSection>
  </SinaSectionGroup>
  <!--QQ登录接口参数-->
  <QQSectionGroup>
    <QzoneSection>
      <add key="AppKey" value="101216732"/>
      <add key="AppSecret" value="db9607bcfb4481ba381a2fab107f4ab4"/>
      <add key="CallBackURI" value="http://wigz.net/Apply/outside/qq.aspx"/>
      <add key="AuthorizeURL" value="https://graph.qq.com/oauth2.0/authorize"/>
    </QzoneSection>
  </QQSectionGroup>
  <!--常用参数-->
  <connectionStrings>
    <add name="AppMSSQL" connectionString="data source=.;uid=sa;pwd=******;database=eshop" providerName="System.Data.SqlClient"/>
    <add name="conn" connectionString="AppMSSQL"/>
    <add name="route" connectionString="/"/>
    <add name="Video" connectionString="/"/>
    <add name="Copyright" connectionString="Copyright © 2015 ESHOP.COM All Right Reserved"/>
    <add name="Email" connectionString="<EMAIL>"/>
    <add name="WPage" connectionString="localhost"/>
    <add name="FPath" connectionString="localhost"/>
    <add name="Judge" connectionString="true"/>
  </connectionStrings>
  <!--重写规则-->
  <RewriterConfig>
    <Rules>
      <RewriterRule>
        <LookFor>~/item.html</LookFor>
        <SendTo>~/item.aspx</SendTo>
      </RewriterRule>
    </Rules>
  </RewriterConfig>
  <!--
    有关 web.config 更改的说明，请参见 http://go.microsoft.com/fwlink/?LinkId=235367。

    可在 <httpRuntime> 标记上设置以下特性。
      <system.Web>
        <httpRuntime targetFramework="4.8" />
      </system.Web>
  -->
  <system.web>
    <!--注册httpHandler-->
    <httpHandlers>
      <add verb="*" path="*.aspx" type="URLRewriter.RewriterFactoryHandler, URLRewriter"/>
      <add verb="*" path="*.html" type="URLRewriter.RewriterFactoryHandler, URLRewriter"/>
    </httpHandlers>
    <compilation defaultLanguage="c#" debug="true" targetFramework="4.8"/>
    <!--身份验证-->
    <authentication mode="Forms">
      <forms name="loginInfo" loginUrl="admin/login.aspx" protection="All" defaultUrl="admin/"/>
    </authentication>
    <!--全局设置-->
    <globalization requestEncoding="utf-8" responseEncoding="utf-8"/>
    <!--错误处理-->
    <customErrors mode="Off" defaultRedirect="error.aspx">
      <error statusCode="403" redirect="error.aspx?tip=您所访问的网页不存在！"/>
      <error statusCode="404" redirect="error.aspx?tip=您所访问的网页不存在！"/>
    </customErrors>
    <!--上传文件大小，超时时间-->
    <httpRuntime maxRequestLength="951200" useFullyQualifiedRedirectUrl="true" minFreeThreads="8" minLocalRequestFreeThreads="4" appRequestQueueLimit="100"/>
    <roleManager enabled="true"/>
    <sessionState mode="InProc" timeout="60"/>
    <pages validateRequest="false"/>
  </system.web>
</configuration>