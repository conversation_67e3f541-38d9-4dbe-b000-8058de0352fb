<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aJ="INSERT";var aa=lang["DlgComInsert"];var lM="";var bj="#CCCCCC";var kn="#000000";var ys=true;if(C.ai()=="Control"){var bg=C.ax();if(bg.tagName=="APPLET"){if(bg.getAttribute("code",2).toLowerCase()=="webeq3.viewercontrol"){aJ="MODI";aa=lang["DlgComModify"];lM=uy(bg.innerHTML,"eq");lM=lM.replace(/&amp;/,"&");bj=uy(bg.innerHTML,"background");kn=uy(bg.innerHTML,"foreground");}}}var bm=lang["DlgEQ"]+"("+aa+")";document.write("<title>"+bm+"</title>");function uy(html,zu){var re=new RegExp("<param name=\""+zu+"\" value=\"(.*?)\">","gi");var wh=re.exec(html);if(wh){return wh[1];}return "";};function aq(){lang.ag(document);try{$("d_eq").setMathML(lM);}catch(e){ys=false;}if(ys){$("v_normal").style.display="";$("d_bgcolor").value=bj;$("s_bgcolor").style.backgroundColor=bj;$("d_forecolor").value=kn;$("s_forecolor").style.backgroundColor=kn;}else{$("v_install").style.display="";$("v_installing").innerHTML="<OBJECT CLASSID='clsid:41649A90-B484-11d1-8D75-00C04FC24EE6' CODEBASE='WebEQInstall.cab#Version=3,0,1,6' HEIGHT=1 WIDTH=1></OBJECT>";}parent.ar(bm);};function ok(){bj=$("d_bgcolor").value;kn=$("d_forecolor").value;lM=$("d_eq").getPackedMathML();if(!oV(bj)){bX($("d_bgcolor"),lang["ErrColorBg"]);return;}if(bj==""){bj="#FFFFFF";}if(kn==""){kn="#000000";}EWIN.insertHTML("<APPLET codeBase=./ height=100 width=320 code=webeq3.ViewerControl><PARAM NAME=\"foreground\" VALUE=\""+kn+"\"><PARAM NAME=\"background\" VALUE=\""+bj+"\"><PARAM NAME=\"size\" VALUE=\"18\"><PARAM NAME=\"eq\" VALUE=\""+lM+"\"></APPLET>");parent.bV();} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table id=v_install border=0 cellpadding=5 cellspacing=10 style="display:none" align=center> <tr><td bgcolor=#ffffff noWrap><span lang=DlgEQInstallAlt></span></td></tr> </table> <div id=v_installing style="display:none"></div> <table border=0 cellpadding=0 cellspacing=0 align=center id=v_normal style="display:none"> <tr> <td colspan=2> <object code='webeq3.editor.InputControl' width=620 height=200 id=d_eq MAYSCRIPT><param name=eq value=''></object> </td> </tr> <tr><td height=8 colspan=2></td></tr> <tr> <td noWrap><span lang=DlgEQBackground></span>:<input type=text id=d_bgcolor size=7 value=""><img border=0 src="images/rect.gif" width=18 style="cursor:hand" id=s_bgcolor onclick="hu('bgcolor')" align=absmiddle>&nbsp; <span lang=DlgEQForeground></span>:<input type=text id=d_forecolor size=7 value=""><img border=0 src="images/rect.gif" width=18 style="cursor:hand" id=s_forecolor onclick="hu('forecolor')" align=absmiddle></td> <td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel></td> </tr> </table> </td></tr></table> </body> </html> 