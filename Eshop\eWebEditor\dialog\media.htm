<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aJ="INSERT";var aa=lang["DlgComInsert"];var t;var bJ="http://";var bO="480";var bD="400";var fF="";var cD="";var cj="";var du="";var cn="true";var eB="";var et="file";var cu=((parseFloat(config.AllowMediaSize)>0)?true:false);vh();var bm=lang["DlgMedia"]+"("+aa+")";document.write("<title>"+bm+"</title>");function vh(){if(C.ai()!="Control"){return;}var da;t=C.ax();if(t.tagName=="IMG"){da=xV(t);if(da=="mediaplayer6"||da=="mediaplayer7"||da=="realplayer"||da=="quicktime"||da=="flv"||da=="vlc"){aJ="MODI";}}if(aJ!="MODI"){return;}aa=lang["DlgComModify"];et="url";fF=da;cD=dE(t,"align");bO=lZ(eZ(t,"width"));bD=lZ(eZ(t,"height"));cj=dE(t,"vspace");du=dE(t,"hspace");aj.Init(wi(t));switch(da){case "mediaplayer6":if(aj.oA=="object"){bJ=aj.GetValue("filename");}else{bJ=aj.GetValue("src");}cn=aj.GetValue("autostart");break;case "mediaplayer7":if(aj.oA=="object"){bJ=aj.GetValue("url");}else{bJ=aj.GetValue("src");}cn=aj.GetValue("autostart");break;case "realplayer":bJ=aj.GetValue("src");cn=aj.GetValue("autostart");break;case "quicktime":bJ=aj.GetValue("src");cn=aj.GetValue("autoplay");break;case "vlc":bJ=aj.GetValue("mrl");cn=aj.GetValue("autoplay");break;case "flv":var yh=aj.GetValue("flashvars");bJ=wT(yh,"file");cn=wT(yh,"autostart");eB=wT(yh,"image");break;}if(cn=="-1"||cn=="1"){cn="true";}else if(cn=="0"){cn="false";}};function wT(yA,ay){var re=new RegExp('^[\\s\\S]*?\\b'+ay+'\\s*?=\\s*?([^&=]+)(?=[&$])[\\s\\S]*?$','gi');if(re.test(yA)){return yA.replace(re,'$1')}else{return '';}};function bP(what){if(what=="url"){$("d_fromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}$("d_checkfromurl").checked=true;if(cu){$("d_checkfromfile").checked=false;$("uploadfile").disabled=true;}}else{$("d_checkfromurl").checked=false;$("uploadfile").disabled=false;$("d_checkfromfile").checked=true;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}}};function UploadError(an){gd();bP('file');$("divProcessing").style.display="none";try{bX($("uploadfile"),md(an,config.AllowMediaExt,config.AllowMediaSize));}catch(e){}};function UploadSaved(gS){if(config.BaseHref!=""){var cs=gS.substr(gS.lastIndexOf(".")+1);if((cs.toLowerCase()=="flv"&&$("d_plugin").value=="")||($("d_plugin").value=="flv")){gS=AI(qf(gS));}}$("d_fromurl").value=gS;eH();};function eH(){EWIN.insertHTML(uv());parent.bV();};function ok(){$("d_width").value=fe($("d_width").value);$("d_height").value=fe($("d_height").value);$("d_vspace").value=fe($("d_vspace").value);$("d_hspace").value=fe($("d_hspace").value);if($("d_checkfromurl").checked){eH();}else{if(!iV($("uploadfile").value,config.AllowMediaExt)){UploadError("ext");return false;}he();$("divProcessing").style.display="";document.myuploadform.submit();}};function he(){$("d_checkfromfile").disabled=true;$("d_checkfromurl").disabled=true;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}$("d_plugin").disabled=true;$("d_align").disabled=true;$("d_autostart").disabled=true;$("d_width").disabled=true;$("d_height").disabled=true;$("d_vspace").disabled=true;$("d_hspace").disabled=true;$("btn_ok").disabled=true;};function gd(){$("d_checkfromfile").disabled=false;$("d_checkfromurl").disabled=false;$("d_fromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}$("d_plugin").disabled=false;$("d_align").disabled=false;$("d_autostart").disabled=false;$("d_width").disabled=false;$("d_height").disabled=false;$("d_vspace").disabled=false;$("d_hspace").disabled=false;$("btn_ok").disabled=false;};function eT(){$("tdPreview").innerHTML=uv(true);try{if($("d_plugin").value=="vlc"){var c=$("tdPreview").firstChild;if(c){if(c.tagName=="OBJECT"){if(c.width=="0"){c.style.cssText="";c.width="180";c.height="140";}}}}}catch(e){}};function uv(yG,sm){cD=$("d_align").value;bO=$("d_width").value;bD=$("d_height").value;cj=$("d_vspace").value;du=$("d_hspace").value;cn=$("d_autostart").value;fF=$("d_plugin").value;eB=$("d_image").value;if(eB==lang["DlgMediaPreviewImgDesc"]||eB.indexOf(".")<0){eB="";}if(sm){bJ=sm;}else{if(yG){bO="180";bD="140";var aG,v;if($("d_checkfromurl").checked){v=$("d_fromurl").value;aG=v;if(config.BaseHref!=""){aG=qf(aG);}}else{v=$("uploadfile").value;aG="file:///"+v;}bJ=aG;}else{bJ=fk($("d_fromurl").value);}if(bJ=="http://"||bJ=="file:///"){return "";}}bJ=CN(bJ);eB=CN(eB);if(fF==""){var n=bJ.lastIndexOf(".");if(n>0){var cs=bJ.substr(n+1).toLowerCase();switch(cs){case "flv":case "f4v":fF="flv";break;case "asf":case "avi":case "mp3":case "mp4":case "wav":case "mpg":case "mpeg":case "mid":case "midi":case "aif":case "aifc":case "aiff":fF="mediaplayer6";break;case "wmv":case "wma":fF="mediaplayer7";break;case "ra":case "ram":case "rm":case "rmvb":fF="realplayer";break;case "qt":case "mov":fF="quicktime";break;}}}if(sm){if(fF==""){fF="realplayer";}}if(fF==""){alert(lang["DlgMediaMsgPlugin"]);return "";}var qU;if(fF=="flv"){if(yG){qU="../plugin/flvplayer.swf";}else{qU=gh("plugin/flvplayer.swf");}}var fu='';if(bO!=''){fu+=' width="'+bO+'"';}if(bD!=''){fu+=' height="'+bD+'"';}if(cD!=''){fu+=' align="'+cD+'"';}if(cj!=''){fu+=' vspace="'+cj+'"';}if(du!=''){fu+=' hspace="'+du+'"';}var V="";switch(fF){case "mediaplayer6":V='<object classid="clsid:22d6f312-b0f6-11d0-94ab-0080c74c7e95"'+fu+'>'+'<param name="filename" value="'+bJ+'">'+'<param name="autostart" value="'+cn+'">'+'<param name="showcontrols" value="true">'+'<param name="loop" value="true">'+'<embed type="application/x-mplayer2" src="'+bJ+'"'+fu+' autostart="'+cn+'" showcontrols="true" loop="true" pluginspage="http://microsoft.com/windows/mediaplayer/en/download/"></embed>'+'</object>';break;case "mediaplayer7":V='<object classid="clsid:6bf52a52-394a-11d3-b153-00c04f79faa6"'+fu+'>'+'<param name="url" value="'+bJ+'">'+'<param name="autostart" value="'+cn+'">'+'<param name="uimode" value="full">'+'<embed type="application/x-mplayer2" src="'+bJ+'"'+fu+' autostart="'+cn+'" uimode="full" pluginspage="http://microsoft.com/windows/mediaplayer/en/download/"></embed>'+'</object>';break;case "realplayer":V='<object classid="clsid:cfcdaa03-8be4-11cf-b84b-0020afbbccfa"'+fu+'>'+'<param name="src" value="'+bJ+'">'+'<param name="autostart" value="'+cn+'">'+'<param name="controls" value="ImageWindow,ControlPanel,StatusBar">'+'<param name="console" value="Clip1">'+'<embed type="audio/x-pn-realaudio-plugin" src="'+bJ+'"'+fu+' autostart="'+cn+'" controls="ImageWindow,ControlPanel,StatusBar" console="Clip1"></embed>'+'</object>';break;case "quicktime":V='<object classid="clsid:02bf25d5-8c17-4b23-bc80-d3488abddc6b" codebase="http://www.apple.com/qtactivex/qtplugin.cab"'+fu+'>'+'<param name="src" value="'+bJ+'">'+'<param name="autoplay" value="'+cn+'">'+'<param name="controller" value="false">'+'<embed type="video/quicktime" src="'+bJ+'"'+fu+' autoplay="'+cn+'" controller="false" pluginspage="http://www.apple.com/quicktime/download/"></embed>'+'</object>';break;case "vlc":V='<object classid="clsid:9be31822-fdad-461b-ad51-be1d1c159921"'+fu+'>'+'<param name="mrl" value="'+bJ+'">'+'<param name="autoplay" value="'+cn+'">'+'<param name="showdisplay" value="true">'+'<param name="autoloop" value="true">'+'<param name="toolbar" value="true">'+'<embed type="application/x-vlc-plugin" mrl="'+bJ+'"'+fu+' autoplay="'+cn+'" showdisplay="true" autoloop="true" toolbar="true" pluginspage="http://www.videolan.org" version="VideoLAN.VLCPlugin.2"></embed>'+'</object>';break;case "flv":V='<object id="'+Co()+'" classid="clsid:d27cdb6e-ae6d-11cf-96b8-444553540000"'+fu+'>'+'<param name="movie" value="'+qU+'">'+'<param name="flashvars" value="file='+bJ+'&autostart='+cn+'&image='+eB+'&provider=video">'+'<param name="quality" value="high">'+'<param name="allowfullscreen" value="true">'+'<embed type="application/x-shockwave-flash" src="'+qU+'"'+fu+' flashvars="file='+bJ+'&autostart='+cn+'&image='+eB+'&provider=video" quality="high" allowfullscreen="true" pluginspage="http://www.macromedia.com/go/getflashplayer"></embed>'+'</object>';break;default:break;}return V};function CN(aG){return aG.replace(/\?/gi,"%3F").replace(/=/gi,"%3D").replace(/&/gi,"%26");};function Co(){var Bf=EWEB.T.getElementsByTagName("IMG");var cb="";for(var i=0;i<Bf.length;i++){if(Bf[i].className=="ewebeditor__flv"){cb+=dE(Bf[i],"_ewebeditor_fake_value");}}var j=0;while(true){j++;var sB="flv__id__"+j;if(cb.indexOf(sB)<0){return sB;}}};function Eb(){$("tdPreview").innerHTML="";BI();ec.OpenDialog("c_codeImg.htm");};function BI(){var o=$("d_image");if(o.value==lang["DlgMediaPreviewImgDesc"]){o.value="";}};function aq(){lang.ag(document);aC($("d_plugin"),fF.toLowerCase());aC($("d_align"),cD.toLowerCase());aC($("d_autostart"),cn.toLowerCase());if(!cu){et="url";}bP(et);$("d_fromurl").value=bJ;$("d_width").value=bO;$("d_height").value=bD;$("d_vspace").value=cj;$("d_hspace").value=du;if(eB==""){$("d_image").value=lang["DlgMediaPreviewImgDesc"];}else{$("d_image").value=eB;}var o1=$("TD_Right");var o2=$("Fieldset_Right");var h1=o1.offsetHeight;var h2=o2.offsetHeight;if(h1>h2){if(F.as){o2.style.height=h1+"px";}else{o2.style.height=(h1-2)+"px";}}eT();parent.ar(bm);if(cu&&document.documentMode==5){var vt=(et=="url")?false:true;$("uploadfile").disabled=vt;$("uploadfile").disabled= !vt;}};function jS(dO,iq,bZ){if(bZ=="tab_mfu"){iU.Load("media",$(bZ),ck.jp[1].Width+"px",ck.jp[1].Height+"px");}};function MFUReturn(bv){var V="";var eG=bv.split("|");for(var i=0;i<eG.length;i++){var a=eG[i].split("::");if(a.length==3&&a[1]!=""){var cf=a[0].substr(a[0].lastIndexOf("\\")+1);var gI=a[1];V+=uv(false,gI)+"<br>";EWIN.addUploadFile(cf,gI);}}EWIN.insertHTML(V);parent.bV();} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <script type="text/javascript"> if(config.MFUEnable=="1"){ck.lQ([[lang["DlgComTabNormal"],"tab_normal"],[lang["DlgComTabMFU"],"tab_mfu"]]);} </script> <table id="tab_normal" border=0 cellpadding=0 cellspacing=5 align=center> <tr valign=top><td> <table border=0 cellpadding=0 cellspacing=0 align=center width="100%"> <tr> <td> <fieldset> <legend><span lang=DlgMediaSource></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=0 width="300px"><tr><td></td></tr></table> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <script type="text/javascript"> if(cu){document.write("<tr>");document.write("<td noWrap width=\"20%\"><input type=radio id=\"d_checkfromfile\" value=\"1\" onclick=\"bP('file')\"><label for=d_checkfromfile>"+lang["DlgFromFile"]+"</label>:</td>");document.write("<td noWrap width=\"80%\">");document.write(lN("media"));document.write("</td>");document.write("</tr>");} </script> <tr> <td noWrap width="20%"><input type=radio id="d_checkfromurl" value="1" onclick="bP('url')"><label for=d_checkfromurl><span lang=DlgFromUrl></span></label>:</td> <td noWrap width="80%"> <script type="text/javascript"> if(config.AllowBrowse=="1"){document.write("<table border=0 cellpadding=0 cellspacing=0 width='100%'><tr><td width='100%'><input type=text id='d_fromurl' style='width:100%' size=10 value='http://'></td><td><input class='dlgBtnBrowse' type=button id='d_browse' onclick=\"nf('media','fromurl')\" value='"+lang["DlgBtnBrowse"]+"' align=absmiddle></td></tr></table>");}else{document.write("<input type=text id='d_fromurl' style='width:100%' size=10 value='http://'>");} </script> </td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <fieldset> <legend><span lang=DlgMediaEffect></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgMediaPlugin></span>:</td> <td noWrap colspan=4> <select id=d_plugin size=1 style="width:100%"> <option value='' selected lang=DlgMediaPluginAuto></option> <option value='mediaplayer6' lang=DlgMediaPluginWMP6></option> <option value='mediaplayer7' lang=DlgMediaPluginWMP7></option> <option value='realplayer' lang=DlgMediaPluginReal></option> <option value='quicktime' lang=DlgMediaPluginQT></option> <option value='flv' lang=DlgMediaPluginFLV></option> <option value='vlc' lang=DlgMediaPluginVLC></option> </select> </td> </tr> <tr> <td noWrap width="20%"><span lang=DlgAlign></span>:</td> <td noWrap width="29%"> <select id=d_align size=1 style="width:80px"> <option value='' selected lang=DlgComDefault></option> <option value='left' lang=DlgAlignLeft></option> <option value='right' lang=DlgAlignRight></option> <option value='top' lang=DlgAlignTop></option> <option value='middle' lang=DlgAlignMiddle></option> <option value='bottom' lang=DlgAlignBottom></option> <option value='absmiddle' lang=DlgAlignAbsmiddle></option> <option value='absbottom' lang=DlgAlignAbsbottom></option> <option value='baseline' lang=DlgAlignBaseline></option> <option value='texttop' lang=DlgAlignTexttop></option> </select> </td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgMediaAutoStart></span>:</td> <td noWrap width="29%"> <select id=d_autostart size=1 style="width:80px"> <option value='' selected lang=DlgComDefault></option> <option value='true' lang=DlgComYes></option> <option value='false' lang=DlgComNo></option> </select> </td> </tr> <tr> <td noWrap width="20%"><span lang=DlgMediaWidth></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_width size=10 value="480"></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgMediaHeight></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_height size=10 value="400"></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgComVSpace></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_vspace size=10 value="" onkeydown="eR(event);"></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgComHSpace></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_hspace size=10 value="" onkeydown="eR(event);"></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgMediaPreviewImg></span>:</td> <td colspan=4><input style="width:215px" type=text id=d_image size=7 value="" onclick="BI()"><img border=0 src="images/rectimg.gif" width=18 style="cursor:hand" id=s_bgimage onclick="Eb()" align=absmiddle></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> </table> </td><td id="TD_Right"> <fieldset id="Fieldset_Right"> <legend><span lang=DlgComPreview></span></legend> <table border=0 cellpadding=0 cellspacing=5 width="200" height="160" valign=top id=tablePreview> <tr><td colspan=2 bgcolor=#FFFFFF align=center valign=middle id=tdPreview height="100%"> </td></tr> <tr><td id=tdPreviewSize></td><td align=right><input type=button class="dlgBtn" id=btnPreivew onclick="eT()" lang=DlgComPreview></td></tr> </table> </fieldset> </td></tr> <tr><td noWrap align=right colspan=2><input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="$('tdPreview').innerHTML='';parent.bn();" lang=DlgBtnCancel></td></tr> </table> <div id="tab_mfu" style="display:none"></div> </td></tr></table> <div id=divProcessing style="width:200px;height:30px;position:absolute;left:70px;top:50px;display:none"> <table border=0 cellpadding=0 cellspacing=1 bgcolor="#000000" width="100%" height="100%"><tr><td bgcolor=#3A6EA5><marquee align="middle" behavior="alternate" scrollamount="5"><font color=#FFFFFF><span lang=DlgComUploading></span></font></marquee></td></tr></table> </div> </body> </html> 