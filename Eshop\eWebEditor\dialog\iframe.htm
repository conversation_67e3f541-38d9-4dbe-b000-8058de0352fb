<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aJ="INSERT";var aa=lang["DlgComInsert"];var t;var ds="http://";var nG="";var oG="0";var ou="0";var oY="0";var bO="500";var bD="400";if(C.ai()=="Control"){t=C.ax();if(t.tagName=="IFRAME"){aJ="MODI";aa=lang["DlgComModify"];ds=t.src;nG=t.scrolling;oG=t.frameBorder;ou=t.marginHeight;oY=t.marginWidth;bO=t.width;bD=t.height;}}var bm=lang["DlgIframe"]+"("+aa+")";document.write("<title>"+bm+"</title>");function aq(){lang.ag(document);aC($("d_scrolling"),nG.toLowerCase());$("d_url").value=ds;$("d_frameborder").value=oG;$("d_marginheight").value=ou;$("d_marginwidth").value=oY;$("d_width").value=bO;$("d_height").value=bD;parent.ar(bm);};function ok(){nG=$("d_scrolling").options[$("d_scrolling").selectedIndex].value;ds=$("d_url").value;if(!zC(ds)){bX($("d_url"),lang["DlgIframeErrUrl"]);return;}$("d_frameborder").value=fe($("d_frameborder").value);$("d_marginheight").value=fe($("d_marginheight").value);$("d_marginwidth").value=fe($("d_marginwidth").value);oG=$("d_frameborder").value;ou=$("d_marginheight").value;oY=$("d_marginwidth").value;var bO="";if(!fd($("d_width"),lang["DlgIframeErrWidth"]))return;bO=$("d_width").value;var bD="";if(!fd($("d_height"),lang["DlgIframeErrHeight"]))return;bD=$("d_height").value;if(aJ=="MODI"){t.src=ds;t.scrolling=nG;t.frameBorder=oG;t.marginHeight=ou;t.marginWidth=oY;t.width=bO;t.height=bD;}else{EWIN.insertHTML("<iframe src='"+ds+"' scrolling='"+nG+"' frameborder='"+oG+"' marginheight='"+ou+"' marginwidth='"+oY+"' width='"+bO+"' height='"+bD+"'></iframe>");}parent.bV();};function fd(obj,kR){var b=false;if(obj.value!=""){obj.value=parseFloat(obj.value);if(obj.value!="0"){b=true;}}if(b==false){bX(obj,kR);return false;}return true;} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td> <fieldset> <legend><span lang=DlgIframeProperty></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgIframeUrl></span>:</td> <td noWrap width="80%" colspan=4><input type=text id=d_url size=10 value="" style="width:100%"></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgIframeScroll></span>:</td> <td noWrap width="29%"> <select id=d_scrolling size=1 style="width:80px"> <option value='' lang=DlgComDefault></option> <option value='yes' lang=DlgIframeYes></option> <option value='no' lang=DlgIframeNo></option> </select> </td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgIframeBorder></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_frameborder size=10 value=""></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgIframeMarginHeight></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_marginheight size=10 value=""></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgIframeMarginWidth></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_marginwidth size=10 value=""></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgIframeWidth></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_width size=10 value=""></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgIframeHeight></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_height size=10 value=""></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr><td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel></td></tr> </table> </td></tr></table> </body> </html> 