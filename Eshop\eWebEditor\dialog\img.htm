<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aJ="INSERT";var aa=lang["DlgComInsert"];var t;var wI;var bJ="http://";var kB="";var fD="0";var dr="";var mk="";var cD="";var bO="";var bD="";var cj="";var du="";var cZ="";var et="file";var cu=((parseFloat(config.AllowImageSize)>0)?true:false);var FE=(((config.SYWZFlag=="2"||config.SYTPFlag=="2")&&config.SYVNormal=="1")?true:false);if(C.ai()=="Control"){t=C.ax();if(t.tagName=="IMG"&&dE(t,"_ewebeditor_fake_tag")==""){aJ="MODI";aa=lang["DlgComModify"];et="url";bJ=vH(t,"src");kB=dE(t,"alt");fD=dE(t,"border");dr=t.style.borderColor;mk=t.style.filter;cD=dE(t,"align");bO=lZ(eZ(t,"width"));bD=lZ(eZ(t,"height"));cj=dE(t,"vspace");du=dE(t,"hspace");}}var bm=lang["DlgImg"]+"("+aa+")";document.write("<title>"+bm+"</title>");function aq(){lang.ag(document);aC($("d_filter"),mk);aC($("d_align"),cD.toLowerCase());if(!cu){et="url";}bP(et);$("d_fromurl").value=bJ;$("d_alt").value=kB;$("d_border").value=fD;$("d_bordercolor").value=dr;$("s_bordercolor").style.backgroundColor=dr;$("d_width").value=bO;$("d_height").value=bD;$("d_vspace").value=cj;$("d_hspace").value=du;var o1=$("TD_Right");var o2=$("Fieldset_Right");var h1=o1.offsetHeight;var h2=o2.offsetHeight;if(h1>h2){if(F.as){o2.style.height=h1+"px";}else{o2.style.height=(h1-2)+"px";}}eT();if($("d_fromurl")){if(F.as){$("d_fromurl").onpropertychange=nY;}else{R.az($("d_fromurl"),"input",nY);}}parent.ar(bm);if(cu&&document.documentMode==5){var vt=(et=="url")?false:true;$("uploadfile").disabled=vt;$("uploadfile").disabled= !vt;}};function nY(){if(F.as){if(event.propertyName!='value'){return;}}var aG=$("d_fromurl").value;var n=aG.lastIndexOf(".");if(n>=1){if(aG.length>n+1){var cs="|"+aG.substr(n+1)+"|";if("|gif|jpg|jpeg|png|bmp|".indexOf(cs.toLowerCase())>=0){eT();return;}}}uY();};function bP(what){if(what=="url"){$("d_fromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}$("d_checkfromurl").checked=true;if(cu){$("d_checkfromfile").checked=false;$("uploadfile").disabled=true;}}else{$("d_checkfromurl").checked=false;$("uploadfile").disabled=false;$("d_checkfromfile").checked=true;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}}};function UploadError(an){gd();bP('file');$("divProcessing").style.display="none";try{bX($("uploadfile"),md(an,config.AllowImageExt,config.AllowImageSize));}catch(e){}};function UploadSaved(rj,yR){$("d_fromurl").value=rj;cZ=yR;if(config.SLTMode=="0"&&config.SLTFlag=="2"){cZ=rj;}eH();};function eH(){bJ=$("d_fromurl").value;kB=$("d_alt").value;fD=$("d_border").value;dr=$("d_bordercolor").value;mk=$("d_filter").options[$("d_filter").selectedIndex].value;cD=$("d_align").value;bO=$("d_width").value;bD=$("d_height").value;cj=$("d_vspace").value;du=$("d_hspace").value;if(aJ=="MODI"){if(cZ){bq(t,"src",cZ);nP(t,"src",cZ);}else{bq(t,"src",bJ);nP(t,"src",bJ);}bq(t,"alt",kB);bq(t,"border",fD);t.style.borderColor=dr;t.style.filter=mk;bq(t,"align",cD);t.style.width=DB(bO);t.style.height=DB(bD);bq(t,"width","");bq(t,"height","");bq(t,"vspace",cj);bq(t,"hspace",du);if(cZ){EWEB.T.execCommand("CreateLink",false,bJ);var ao=t.parentNode;if(ao.tagName=="A"){bq(ao,"target","_blank");bq(ao,"href",bJ);nP(ao,"href",bJ)}}}else{var V='';if(mk!=''){V=V+'filter:'+mk+';';}if(dr!=''){V=V+'border-color:'+dr+';';}if(bO!=""){V=V+'width:'+bO+'px;';}if(bD!=""){V=V+'height:'+bD+'px;';}if(V!=''){V=' style="'+V+'"';}if(cZ){V='<img src="'+cZ+'"'+V;}else{V='<img src="'+bJ+'"'+V;}if(fD!=''){V=V+' border="'+fD+'"';}if(kB!=''){V=V+' alt="'+kB+'"';}if(cD!=''){V=V+' align="'+cD+'"';}if(cj!=''){V=V+' vspace="'+cj+'"';}if(du!=''){V=V+' hspace="'+du+'"';}V=V+'>';if(cZ){V='<a href="'+bJ+'" target="_blank">'+V+'</a>';}EWIN.insertHTML(V);}parent.bV();};var qg="";function ok(){$("d_border").value=fe($("d_border").value);$("d_width").value=fe($("d_width").value);$("d_height").value=fe($("d_height").value);$("d_vspace").value=fe($("d_vspace").value);$("d_hspace").value=fe($("d_hspace").value);if(!oV($("d_bordercolor").value)){bX($("d_bordercolor"),lang["DlgImgErrBorColor"]);return false;}if($("d_checkfromurl").checked){eH();}else{if(!iV($("uploadfile").value,config.AllowImageExt)){UploadError("ext");return false;}he();$("divProcessing").style.display="";if(FE){if(qg==""){qg=document.myuploadform.action;}if($("d_syflag").checked){document.myuploadform.action=qg+"&syflag=1";}else{document.myuploadform.action=qg;}}document.myuploadform.submit();}};function he(){$("d_checkfromfile").disabled=true;$("d_checkfromurl").disabled=true;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}$("d_alt").disabled=true;$("d_border").disabled=true;$("d_bordercolor").disabled=true;$("d_filter").disabled=true;$("d_align").disabled=true;$("d_width").disabled=true;$("d_height").disabled=true;$("d_vspace").disabled=true;$("d_hspace").disabled=true;$("d_ok").disabled=true;};function gd(){$("d_checkfromfile").disabled=false;$("d_checkfromurl").disabled=false;$("d_fromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}$("d_alt").disabled=false;$("d_border").disabled=false;$("d_bordercolor").disabled=false;$("d_filter").disabled=false;$("d_align").disabled=false;$("d_width").disabled=false;$("d_height").disabled=false;$("d_vspace").disabled=false;$("d_hspace").disabled=false;$("d_ok").disabled=false;};function eT(){var aG="";if($("d_checkfromurl").checked){aG=$("d_fromurl").value;if(config.BaseHref!=""){aG=qf(aG);}}else{if(F.as&& !F.BX){aG=$("uploadfile").value;}else{if($("uploadfile").files.length>0){try{aG=$("uploadfile").files[0].getAsDataURL();}catch(e){aG="";}if(!aG){try{aG=window.URL.createObjectURL($("uploadfile").files[0]);}catch(e){aG="";}}}}}if($("d_checkfromurl").checked||(F.as&& !F.BX)){if(aG.lastIndexOf(".")<=0){uY();return;}}var xR=false;if((cu)&&(F.as)&& !F.BX){if($("d_checkfromfile").checked){xR=true;}}if(aG){if(xR){zt(aG);}else{var img=new Image();img.onload=function(){AS(img.width,img.height,img.src);};img.onerror=uY;img.src=aG;}}else{$("tdPreview").innerHTML="";}};function AS(w,h,aG){var fW=$("tdPreview").offsetWidth;var gU=$("tdPreview").offsetHeight;var sw,fM;if((w>fW)||(h>gU)){var nw=fW/w;var iK=gU/h;if(nw>iK){fM=gU;sw=w*iK;}else{sw=fW;fM=h*nw;}}else{sw=w;fM=h;}$("tdPreview").innerHTML="<img border=0 src='"+aG+"' width='"+sw+"' height='"+fM+"' style=\"filter:"+$("d_filter").value+"\">";$("tdPreviewSize").innerHTML=w+" * "+h;jb=w;jc=h;vr(w,h);};function uY(){$("tdPreview").innerHTML="";$("tdPreviewSize").innerHTML="";jb="";jc="";};var yN="";function zt(url){if($("imgPreviewDiv")&&yN==url){return;}yN=url;$("tdPreview").innerHTML="<div id=imgPreviewDiv style=\"filter : progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod='image') "+$("d_filter").value+";WIDTH:1px; HEIGHT:1px; \"></div>";$("imgPreviewDiv").filters.item("DXImageTransform.Microsoft.AlphaImageLoader").src=url;window.setTimeout("vl()",50);};function vl(){var w=$("imgPreviewDiv").offsetWidth;var h=$("imgPreviewDiv").offsetHeight;if(w==1||h==1){window.setTimeout("vl()",50);return;}var fW=$("tdPreview").offsetWidth;var gU=$("tdPreview").offsetHeight;var sw,fM;if((w>fW)||(h>gU)){var nw=fW/w;var iK=gU/h;if(nw>iK){fM=gU;sw=w*iK;}else{sw=fW;fM=h*nw;}}else{sw=w;fM=h;}$("imgPreviewDiv").style.width=sw;$("imgPreviewDiv").style.height=fM;$("imgPreviewDiv").filters.item("DXImageTransform.Microsoft.AlphaImageLoader").sizingMethod='scale';$("tdPreviewSize").innerHTML=w+" * "+h;jb=w;jc=h;vr(w,h);};function vr(w,h){if(config.SLTFlag!="2"){return;}var qA=parseInt(config.SLTMinSize);var hU=parseInt(config.SLTOkSize);var tw,th;switch(config.SLTCheckFlag){case "0":if(w<=qA){return;}tw=hU;th=parseInt(h*(hU/w));break;case "1":if(h<=qA){return;}tw=parseInt(w*(hU/h));th=hU;break;case "2":if(w<=qA&&h<=qA){return;}if(w>h){tw=hU;th=parseInt(h*(hU/w));}else{tw=parseInt(w*(hU/h));th=hU;}break;}$("d_width").value=tw+"";$("d_height").value=th+"";};var qe=true;var jb="";var jc="";function zb(vC){if(qe){qe=false;vC.src="images/unlock.gif";}else{qe=true;vC.src="images/lock.gif";}};function zf(){if(($("d_width").value==jb+"")&&($("d_height").value==jc+"")){$("d_width").value="";$("d_height").value="";}else{$("d_width").value=jb;$("d_height").value=jc;}};function vJ(an){if(!qe){return;}if(!jb){return;}if(an=="w"){var w=parseInt($("d_width").value);if(isNaN(w)){$("d_width").value="";$("d_height").value="";return;}$("d_height").value=parseInt(w*jc/jb);}else{var h=parseInt($("d_height").value);if(isNaN(h)){$("d_width").value="";$("d_height").value="";return;}$("d_width").value=parseInt(h*jb/jc);}};function jS(dO,iq,bZ){if(bZ=="tab_mfu"){iU.Load("image",$(bZ),ck.jp[1].Width+"px",ck.jp[1].Height+"px");}};function MFUReturn(bv){var V="";var eG=bv.split("|");for(var i=0;i<eG.length;i++){var a=eG[i].split("::");if(a.length==3&&a[1]!=""){var cf=a[0].substr(a[0].lastIndexOf("\\")+1);if(a[2]==""){V+="<img border=0 src='"+a[1]+"'><br><br>";EWIN.addUploadFile(cf,a[1]);}else{V+="<a href='"+a[1]+"' target='_blank'><img border=0 src='"+a[2]+"'></a><br><br>";EWIN.addUploadFile(cf,a[1]);EWIN.addUploadFile(cf,a[2]);}}}EWIN.insertHTML(V);parent.bV();} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <script type="text/javascript"> if(config.MFUEnable=="1"){ck.lQ([[lang["DlgComTabNormal"],"tab_normal"],[lang["DlgComTabMFU"],"tab_mfu"]]);} </script> <table id="tab_normal" border=0 cellpadding=0 cellspacing=5 align=center> <tr valign=top><td> <table border=0 cellpadding=0 cellspacing=0 align=center width="100%"> <tr> <td> <fieldset> <legend><span lang=DlgImgSource></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=0 width="300px"><tr><td></td></tr></table> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <script type="text/javascript"> if(cu){document.write("<tr>");document.write("<td noWrap width=\"20%\"><input type=radio id=\"d_checkfromfile\" value=\"1\" onclick=\"bP('file')\"><label for=d_checkfromfile>"+lang["DlgFromFile"]+"</label>:</td>");document.write("<td noWrap width=\"80%\">");document.write(lN("image"));document.write("</td>");document.write("</tr>");} </script> <tr> <td noWrap width="20%"><input type=radio id="d_checkfromurl" value="1" onclick="bP('url')"><label for=d_checkfromurl><span lang=DlgFromUrl></span></label>:</td> <td noWrap width="80%"> <script type="text/javascript"> if(config.AllowBrowse=="1"){document.write("<table border=0 cellpadding=0 cellspacing=0 width='100%'><tr><td width='100%'><input type=text id='d_fromurl' style='width:180px' size=20 value=''></td><td><input class='dlgBtnBrowse' type=button id='d_browse' onclick=\"nf('image','fromurl')\" value='"+lang["DlgBtnBrowse"]+"' align=absmiddle></td></tr></table>");}else{document.write("<input type=text id='d_fromurl' style='width:242px' size=28 value=''>");} </script> </td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <fieldset> <legend><span lang=DlgImgEffect></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgImgAlt></span>:</td> <td noWrap width="80%" colspan=4><input type=text id=d_alt size=10 value="" style="width:99%"></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgImgBorder></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_border size=10 value="" onkeydown="eR(event);"></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgImgBorderColor></span>:</td> <td noWrap width="29%"><input style="width:62px" type=text id=d_bordercolor size=7 value=""><img border=0 src="images/rect.gif" width=18 style="cursor:hand" id=s_bordercolor onclick="hu('bordercolor')" align=absmiddle></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgImgSpecEffect></span>:</td> <td noWrap width="29%"> <select id=d_filter size=1 style="width:80px" onchange="eT()"> <option value='' selected lang=DlgComNone></option> <option value='Alpha(Opacity=50)' lang=DlgImgAlpha1></option> <option value='Alpha(Opacity=0, FinishOpacity=100, Style=1, StartX=0, StartY=0, FinishX=100, FinishY=140)' lang=DlgImgAlpha2></option> <option value='Alpha(Opacity=10, FinishOpacity=100, Style=2, StartX=30, StartY=30, FinishX=200, FinishY=200)' lang=DlgImgAlpha3></option> <option value='blur(add=1,direction=14,strength=15)' lang=DlgImgBlur1></option> <option value='blur(add=true,direction=45,strength=30)' lang=DlgImgBlur2></option> <option value='Wave(Add=0, Freq=60, LightStrength=1, Phase=0, Strength=3)' lang=DlgImgWave></option> <option value='gray' lang=DlgImgGray></option> <option value='Chroma(Color=#FFFFFF)' lang=DlgImgChroma></option> <option value='DropShadow(Color=#999999, OffX=7, OffY=4, Positive=1)' lang=DlgImgDropShadow></option> <option value='Shadow(Color=#999999, Direction=45)' lang=DlgImgShadow></option> <option value='Glow(Color=#ff9900, Strength=5)' lang=DlgImgGlow></option> <option value='flipv' lang=DlgImgFlipv></option> <option value='fliph' lang=DlgImgFliph></option> <option value='grays' lang=DlgImgGrays></option> <option value='xray' lang=DlgImgXray></option> <option value='invert' lang=DlgImgInvert></option> </select> </td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgAlign></span>:</td> <td noWrap width="29%"> <select id=d_align size=1 style="width:80px"> <option value='' selected lang=DlgComDefault></option> <option value='left' lang=DlgAlignLeft></option> <option value='right' lang=DlgAlignRight></option> <option value='top' lang=DlgAlignTop></option> <option value='middle' lang=DlgAlignMiddle></option> <option value='bottom' lang=DlgAlignBottom></option> <option value='absmiddle' lang=DlgAlignAbsmiddle></option> <option value='absbottom' lang=DlgAlignAbsbottom></option> <option value='baseline' lang=DlgAlignBaseline></option> <option value='texttop' lang=DlgAlignTexttop></option> </select> </td> </tr> <tr> <td noWrap width="20%"><span lang=DlgImgWidth></span>:</td> <td noWrap width="29%"><input style="width:62px" type=text id=d_width size=10 value="" onchange="vJ('w')" onkeyup="vJ('w')"><input type=image src="images/lock.gif" width=16 border=0 align=absmiddle onclick="zb(this)"></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgImgVSpace></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_vspace size=10 value="" onkeydown="eR(event);"></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgImgHeight></span>:</td> <td noWrap width="29%"><input style="width:62px" type=text id=d_height size=10 value="" onchange="vJ('h')" onkeyup="vJ('h')"><input type=image src="images/refresh.gif" width=16 border=0 align=absmiddle onclick="zf()"></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgImgHSpace></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_hspace size=10 value="" onkeydown="eR(event);"></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> </table> </td><td id="TD_Right"> <fieldset id="Fieldset_Right"> <legend><span lang=DlgImgPreview></span></legend> <div style="width:200px;height:200px"> <table border=0 cellpadding=0 cellspacing=5 width="200" height="200" valign=top style="table-layout:fixed;"> <tr><td colspan=2 bgcolor=#FFFFFF align=center valign=middle id=tdPreview height="150"> </td></tr> <tr><td id=tdPreviewSize></td><td align=right><input type=button class="dlgbtn" id=btnPreivew onclick="eT()" lang=DlgImgPreview></td></tr> </table> </div> </fieldset> </td></tr> <tr> <td noWrap> <script type="text/javascript"> if(FE){document.write("<input type=checkbox id=d_syflag value='1'><label for=d_syflag><span lang=DlgImgSYFlag></span></label>");} </script> </td> <td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="d_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel></td> </tr> </table> <div id="tab_mfu" style="display:none;"></div> </td></tr></table> <div id=divProcessing style="width:200px;height:30px;position:absolute;left:80px;top:100px;display:none"> <table border=0 cellpadding=0 cellspacing=1 bgcolor="#000000" width="100%" height="100%"><tr><td bgcolor=#3A6EA5><marquee align="middle" behavior="alternate" scrollamount="5"><font color=#FFFFFF><span lang=DlgComUploading></span></font></marquee></td></tr></table> </div> </body> </html>