<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aJ="INSERT";var aa=lang["DlgComInsert"];var t;var gb="";var jH="";if(C.ai()=="Control"){t=C.ax();if(t.tagName=="MARQUEE"){aJ="MODI";aa=lang["DlgComModify"];jH=t.behavior;gb=t.innerHTML;}}var bm=lang["DlgMarquee"]+"("+aa+")";document.write("<title>"+bm+"</title>");function aq(){lang.ag(document);$("d_text").value=gb;switch(jH){case "scroll":$("d_behavior_scroll").checked=true;break;case "slide":$("d_behavior_slide").checked=true;break;default:jH="alternate";$("d_behavior_alternate").checked=true;break;}parent.ar(bm);};function ok(){gb=$("d_text").value;jH=Ag("d_behavior");if(aJ=="MODI"){t.behavior=jH;t.innerHTML=gb;}else{EWIN.insertHTML("<marquee behavior='"+jH+"'>"+gb+"</marquee>");}parent.bV();};function Ag(ay){var K=document.getElementsByName(ay);for(var i=0;i<K.length;i++){if(K[i].checked){return K[i].value;}}} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr><td> <FIELDSET> <LEGEND></LEGEND> <table border=0 cellspacing=0 cellpadding=5 width="100%"> <tr><td> <table border=0 cellspacing=2 cellpadding=0 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgMarqueeText></span>:</td> <td noWrap width="80%"><input type=text id="d_text" size=40 value=""></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgMarqueeBehavior></span>:</td> <td noWrap width="80%"><input type="radio" name="d_behavior" id="d_behavior_scroll" value="scroll"><label for="d_behavior_scroll"><span lang=DlgMarqueeScroll></span></label> <input type="radio" name="d_behavior" id="d_behavior_slide" value="slide"><label for="d_behavior_slide"><span lang=DlgMarqueeSlide></span></label> <input type="radio" name="d_behavior" id="d_behavior_alternate" value="alternate"><label for="d_behavior_alternate"><span lang=DlgMarqueeAlternate></span></label></td> </tr> </table> </td></tr> </table> </FIELDSET> </td></tr> <tr><td height=10></td></tr> <tr><td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel></td></tr> </table> </td></tr></table> </body> </html> 