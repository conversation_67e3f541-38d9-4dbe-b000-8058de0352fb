<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var BJ=true;var aJ=cc["action"];var jy=cc["autodone"];var iB="";if(aJ=="paste"){iB="("+lang["DlgWordPaste"]+")";}else{aJ="import";}var bm=lang["DlgWord"]+iB;document.write("<title>"+bm+"</title>");var ll=(parseInt(config.WIAPI)<10)?true:false;var FE=(((config.SYWZFlag=="2"||config.SYTPFlag=="2")&&config.SYVLocal=="1")?true:false);function ok(){if(DLGRunOne.FH("btn_ok")){return;}DLGRunOne.BP("btn_ok",true);if(!FT.FV(true)){DLGRunOne.BP("btn_ok",false);return;}if(aJ!="paste"&&BJ){if($("d_modeimg").checked){if(!FT.FO(true)){DLGRunOne.BP("btn_ok",false);return;}}}var es="";if(aJ!="paste"){es=$("d_file").value;if(!iV(es,"doc|docx|wps")){alert(lang["DlgWordInvalidFile"]);DLGRunOne.BP("btn_ok",false);return;}}var eL="";if($("d_imgjpg").checked){eL="jpg";}else if($("d_imggif").checked){eL="gif";}else if($("d_imgpng").checked){eL="png";}var cw="";if(aJ!="paste"){if(ll){if($("d_api0").checked){cw="0";}else if($("d_api1").checked){cw="1";}else{cw="2";}}else{cw=config.WIAPI.substr(1);}}var bu="";bu+="api:"+cw+";";bu+="mode:"+($("d_modehtml").checked?"html":"img")+";";bu+="imgtype:"+eL+";";bu+="optimizemode:"+($("d_opt2").checked?"2":"1")+";";bu+="opt1vml:1;";bu+="opt1css:1;";bu+="opt1absolute:"+($("d_opt1absolute").checked?"1":"0")+";";bu+="opt1eq:"+($("d_opt1eq").checked?"1":"0")+";";bu+="opt1margin:"+($("d_opt1margin").checked?"1":"0")+";";bu+="opt1space:"+($("d_opt1space").checked?"1":"0")+";";bu+="opt1grid:"+($("d_opt1grid").checked?"1":"0")+";";bu+="opt2image:"+($("d_opt2image").checked?"1":"0")+";";bu+="opt2table:"+($("d_opt2table").checked?"1":"0")+";";bu+="opt2eq:"+($("d_opt2eq").checked?"1":"0")+";";bu+="opt2indent:"+($("d_opt2indent").checked?"1":"0")+";";bu+="opt2ptobr:"+($("d_opt2ptobr").checked?"1":"0")+";";bu+="pagewidth:"+($("d_pagewidth").checked?"1":"0")+";";bu+="pagemargin:"+($("d_pagemargin").checked?"1":"0")+";";bu+="pageeffect:"+($("d_pageeffect").checked?"1":"0")+";";bu+="pagescroll:"+($("d_pagescroll").checked?"1":"0")+";";if(aJ!="paste"){bu+="printbg:"+($("d_printbg").checked?"1":"0")+";";}if(BJ){bu+="printimage:1;";}$("divProcessing").style.display="";if(FE){if($("d_syflag").checked){FT.kI.SendUrl=EWEB.SendUrl+"&syflag=1";}}if(aJ!="paste"){FT.kI.ImportWord(es,bu);}else{FT.kI.PasteWord(bu);}window.setTimeout(hT,1000);};function hT(){if(FT.kI.Status!="ok"){window.setTimeout(hT,300);return;}if(FT.Fi()){$("divProcessing").style.display="none";DLGRunOne.BP("btn_ok",false);return;}var ak=FT.kI.Style;if($("d_opt2").checked){ak="";}var bC=FT.kI.Body;EWIN.addUploadFiles(FT.kI.OriginalFiles,FT.kI.SavedFiles);if(aJ!="paste"){if($("d_modeimg").checked){if($("d_imgefflag").checked){bC='<div class="ewebeditor_doc" style="width:'+$('d_imgefwidth').value+';height:'+$('d_imgefheight').value+'; overflow:auto;background-color:#A0A0A3;border:1px solid #D4D0C8;text-align:center;">'+bC.replace(/(<img)(\s[^>]*?>)/gi,'$1 style="border-width:1px 2px 2px 1px;border-color:#00000;margin:5px;"$2')+'</div>';}}}if($("d_pos").checked){EWIN.setHTML(ak+bC,true);}else{EWIN.insertHTML(bC);if(ak){var fh=ak+EWIN.getHTML();EWIN.setHTML(fh,true);}}$("divProcessing").style.display="none";parent.bV({flag:"AfterImportWord",action:aJ});};function hX(index){var jZ,K;for(var i=1;i<=2;i++){jZ=$("group_opt"+i);if(index==i){pf(jZ,false);}else{pf(jZ,true);}}};function pf(bd,gK){pz(bd,gK,"INPUT");pz(bd,gK,"SPAN");};function pz(bd,gK,aH){var K=bd.getElementsByTagName(aH);for(var j=0;j<K.length;j++){K[j].disabled=gK;}};function zL(obj){if(obj.checked){$("d_opt2indent").checked=false;}};function zA(obj){if(obj.checked){$("d_opt2ptobr").checked=false;}};function vO(b){if(b){$("d_pagemargin").disabled=false;$("d_pageeffect").disabled=false;$("d_pagescroll").disabled=false;}else{$("d_pagemargin").disabled=true;$("d_pageeffect").disabled=true;$("d_pagescroll").disabled=true;$("d_pagemargin").checked=false;$("d_pageeffect").checked=false;$("d_pagescroll").checked=false;}};function oj(flag){if(flag==1){$("tab_modehtml").style.display="";$("tab_modeimg").style.display="none";}else{$("tab_modeimg").style.height=$("tab_modehtml").offsetHeight;$("tab_modeimg").style.width=$("tab_modehtml").offsetWidth;$("tab_modehtml").style.display="none";$("tab_modeimg").style.display="";}};function pK(){var b=$("d_imgefflag").checked;pf($("group_imgef"),!b);};function uc(){if(jy){window.setTimeout("ok()",100);}};function aq(){lang.ag(document);if(config.WIIMode=="2"){$("d_opt2").checked=true;hX(2);}else{$("d_opt1").checked=true;hX(1);}if(aJ=="paste"){$("d_pos").checked=false;$("d_pagewidth").checked=false;vO(false);}else{switch(config.WIAPI){case "0":case "10":bU(0);break;case "1":case "11":bU(1);break;case "2":case "12":bU(2);break;}if(ll){$("d_api"+config.WIAPI).checked=true;}pK();}parent.ar(bm);kx();if(jy){ok();}};function kx(){var el=$("divProcessing");var dw=$("tabDialogSize").offsetWidth;var pB=$("tabDialogSize").offsetHeight;if(dw<50||pB<50){window.setTimeout("kx()",100);}el.style.left=(dw+6-parseInt(el.style.width))/2+"px";el.style.top=(pB-parseInt(el.style.height))/2+"px";};function bU(AW){if(BJ){return;}if(AW==1){$("sp_modeimg").style.display="";}else{oj(1);$("d_modehtml").checked=true;$("sp_modeimg").style.display="none";}};function DO(){if(!FT.FV(true)){return;}var es=FT.kI.DialogOpen(1,0,lang["DlgWordFile"]+"(*.doc,*.docx,*.wps)|*.doc;*.docx;*.wps",1,"","");if(es){$("d_file").value=es;}} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <script type="text/javascript"> if(aJ!="paste"){document.write("<tr>");document.write("	<td>");document.write("	<fieldset>");document.write("	<legend><span lang=DlgWordLegend></span>:</legend>");document.write("	<table border=0 cellpadding=5 cellspacing=0 width='100%'><tr><td>");document.write("	<table border=0 cellpadding=0 cellspacing=2 width='100%'>");document.write("		<tr>");document.write("			<td noWrap><span lang=DlgWordFile></span>:</td>");document.write("			<td noWrap width='100%'><input type=text id='d_file' size=30 style='width:270px'> <input type=button class='dlgBtnCommon dlgBtn' lang=DlgBtnBrowse onclick='DO()'></td>");document.write("		</tr>");if(ll){document.write("		<tr>");document.write("			<td noWrap><span lang=DlgWordAPI></span>:</td>");document.write("			<td noWrap><input type=radio name=d_api id=d_api0 onclick='bU(0)' checked><label for=d_api0>"+lang["DlgWordAPI0"]+"</label>&nbsp; <input type=radio name=d_api id=d_api1 onclick='bU(1)' checked><label for=d_api1>"+lang["DlgWordAPI1"]+"</label>&nbsp; <input type=radio name=d_api id=d_api2 onclick='bU(2)'><label for=d_api2>"+lang["DlgWordAPI2"]+"</label></td>");document.write("		</tr>");}document.write("	</table>");document.write("	</td></tr></table>");document.write("	</fieldset>");document.write("	</td>");document.write("</tr>");document.write("<tr><td height=5></td></tr>");} </script> <tr> <td> <fieldset> <legend><span lang=DlgWordOptimize></span>: <input type=radio id=d_modehtml name=g_mode checked onclick="oj(1)"><label for=d_modehtml><span lang=DlgWordModeHTML></span></label>&nbsp;<span id=sp_modeimg><input type=radio id=d_modeimg name=g_mode onclick="oj(2)"><label for=d_modeimg><span lang=DlgWordModeIMG></span></label></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td valign=top> <table border=0 cellpadding=0 cellspacing=3 id=tab_modehtml> <tr><td colspan=6><input type=radio name="d_optimize" id="d_opt1" checked onclick="hX(1)"><label for=d_opt1><span lang=DlgWordOpt1></span></label></td></tr> <tr id=group_opt1> <td>&nbsp;&nbsp;&nbsp; </td> <td noWrap><input type=checkbox id=d_opt1absolute checked><label for=d_opt1absolute><span lang=DlgWordOpt1Absolute></span></label></td> <td noWrap><input type=checkbox id=d_opt1eq checked><label for=d_opt1eq><span lang=DlgWordOpt1EQ></span></label></td> <td noWrap><input type=checkbox id=d_opt1margin checked><label for=d_opt1margin><span lang=DlgWordOpt1Margin></span></label></td> <td noWrap><input type=checkbox id=d_opt1space><label for=d_opt1space><span lang=DlgWordOpt1Space></span></label></td> <td noWrap><input type=checkbox id=d_opt1grid><label for=d_opt1grid><span lang=DlgWordOpt1Grid></span></label></td> </tr> <tr><td colspan=6><input type=radio name="d_optimize" id="d_opt2" onclick="hX(2)"><label for=d_opt2><span lang=DlgWordOpt2></span></label></td></tr> <tr id=group_opt2> <td>&nbsp; </td> <td noWrap><input type=checkbox id=d_opt2image checked><label for=d_opt2image><span lang=DlgWordOpt2Image></span></label></td> <td noWrap><input type=checkbox id=d_opt2table checked><label for=d_opt2table><span lang=DlgWordOpt2Table></span></label></td> <td noWrap><input type=checkbox id=d_opt2eq checked><label for=d_opt2eq><span lang=DlgWordOpt2EQ></span></label></td> <td noWrap><input type=checkbox id=d_opt2indent onclick="zA(this)"><label for=d_opt2indent><span lang=DlgWordOpt2Indent></span></label></td> <td noWrap><input type=checkbox id=d_opt2ptobr onclick="zL(this)"><label for=d_opt2ptobr><span lang=DlgWordOpt2PtoBR></span></label></td> </tr> <tr><td colspan=6 height=1><hr size=1 color="#999999"></td></tr> <tr> <td colspan=2 align=right><span lang=DlgWordPage></span>:</td> <td noWrap><input type=checkbox id=d_pagewidth onclick="vO(this.checked)"><label for=d_pagewidth><span lang=DlgWordPageWidth></span></label></td> <td noWrap><input type=checkbox id=d_pagemargin><label for=d_pagemargin><span lang=DlgWordPageMargin></span></label></td> <td noWrap><input type=checkbox id=d_pageeffect><label for=d_pageeffect><span lang=DlgWordPageEffect></span></label></td> <td noWrap><input type=checkbox id=d_pagescroll><label for=d_pagescroll><span lang=DlgWordPageScroll></span></label></td> </tr> </table> <table border=0 cellpadding=0 cellspacing=3 id=tab_modeimg style="display:none"> <tr> <td noWrap><span lang=DlgWordImgType></span>: <input type=radio id=d_imggif name=d_imgtype><label for=d_imggif>GIF</label> <input type=radio id=d_imgjpg name=d_imgtype><label for=d_imgjpg>JPG</label> <input type=radio id=d_imgpng name=d_imgtype checked><label for=d_imgpng>PNG</label></td> </tr> <script type="text/javascript"> if(FE){document.write("<tr><td><span lang=DlgImgSYFlag></span>: <input type=checkbox id=d_syflag value='1'></td></tr>");}if(aJ!="paste"){document.write("<tr><td><input type=checkbox id=d_printbg value='1'><label for=d_printbg><span lang=DlgWordPrintBg></span></label></td></tr>");document.write("<tr><td><input type=checkbox id=d_imgefflag value='1' onclick='pK()' checked><label for=d_imgefflag><span lang=DlgWordImgEfFlag></span>:</label> <span id=group_imgef><span lang=DlgWordImgEfWidth></span><input type=text size=5 id=d_imgefwidth style='width:50px' value='100%'>&nbsp;<span lang=DlgWordImgEfHeight></span><input type=text size=5 id=d_imgefheight style='width:50px' value=''></span></td></tr>");} </script> <tr><td><span lang=DlgWordImgAlt></span></td></tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <table border=0 cellpadding=0 cellspacing=0 width="100%"> <tr> <td noWrap><input type=checkbox id=d_pos checked><label for=d_pos><span lang=DlgComInsertReplace></span></label></td> <td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel> </tr> </table> </td> </tr> </table> </td></tr></table> <div id=divProcessing style="width:200px;height:30px;position:absolute;left:50px;top:30px;display:none"> <table border=0 cellpadding=0 cellspacing=1 bgcolor="#000000" width="100%" height="100%"><tr><td bgcolor=#3A6EA5 align=center><marquee align="middle" behavior="alternate" scrollamount="5"><font color=#FFFFFF><span lang=DlgWordImporting></span></font></marquee></td></tr></table> </div> </body> </html> 